// Advanced Filters Diagnostic Script
// This script helps diagnose issues with the advanced filters functionality

(function() {
    'use strict';
    
    function runDiagnostic() {
        console.log('=== Advanced Filters Diagnostic ===');
        
        // Check if elements exist
        const toggle = document.getElementById('advancedFiltersToggle');
        const collapse = document.getElementById('advancedFilters');
        
        console.log('Toggle element:', toggle ? 'Found' : 'NOT FOUND');
        console.log('Collapse element:', collapse ? 'Found' : 'NOT FOUND');
        
        if (toggle) {
            console.log('Toggle aria-expanded:', toggle.getAttribute('aria-expanded'));
            console.log('Toggle classes:', toggle.className);
        }
        
        if (collapse) {
            console.log('Collapse classes:', collapse.className);
            console.log('Collapse display style:', window.getComputedStyle(collapse).display);
            console.log('Collapse has "show" class:', collapse.classList.contains('show'));
        }
        
        // Check Bootstrap
        console.log('Bootstrap available:', typeof window.bootstrap !== 'undefined');
        if (window.bootstrap && collapse) {
            const instance = bootstrap.Collapse.getInstance(collapse);
            console.log('Bootstrap Collapse instance:', instance ? 'Found' : 'NOT FOUND');
        }
        
        // Check screen size
        console.log('Screen width:', window.innerWidth);
        console.log('Is desktop (>= 768px):', window.innerWidth >= 768);
        
        console.log('=== End Diagnostic ===');
    }
    
    // Run diagnostic when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runDiagnostic);
    } else {
        runDiagnostic();
    }
    
    // Also run after a delay to catch any dynamic changes
    setTimeout(runDiagnostic, 1000);
    
    // Make function available globally for manual testing
    window.runAdvancedFiltersDiagnostic = runDiagnostic;
})();
