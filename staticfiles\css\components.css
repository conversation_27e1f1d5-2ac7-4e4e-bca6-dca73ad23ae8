/* ========================================
   RECIPE FINDER - COMPONENT STYLES
   Reusable UI components including buttons, forms, navigation, cards, and modals
   ======================================== */

/* ========================================
   BUTTONS
   ======================================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  white-space: nowrap;
  min-height: 44px;
  min-width: 44px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-on-primary);
  border-color: var(--primary-color);
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--accent-color) 100%);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
  color: var(--text-on-secondary);
  border-color: var(--secondary-color);
}

.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active {
  background: linear-gradient(135deg, var(--secondary-dark) 0%, var(--accent-color) 100%);
  border-color: var(--secondary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
}

.btn-outline {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  color: #374151;
}

.btn-outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

/* ========================================
   FORM ELEMENTS
   ======================================== */
.form-control,
.search-input,
input[type="text"],
input[type="search"],
input[type="email"],
textarea,
select {
  width: 100%;
  padding: 12px 16px;
  font-size: 16px; /* Prevent zoom on iOS */
  border: 2px solid #e5e7eb;
  border-radius: var(--radius-lg);
  background-color: #ffffff;
  color: var(--text-dark);
  transition: all var(--transition-base);
  min-height: 48px;
}

.form-control:focus,
.search-input:focus,
input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-control::placeholder,
.search-input::placeholder,
input::placeholder,
textarea::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

/* Search Form Components */
.search-container {
  max-width: 800px;
  margin: 0 auto;
}

.search-form-wrapper {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 4px;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-slow);
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-form-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1), var(--shadow-md);
}

.search-type-select {
  background: #f9fafb;
  border: none;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all var(--transition-base);
  min-width: 120px;
}

.search-type-select:focus {
  outline: none;
  background: #f3f4f6;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.search-btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-base);
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
  justify-content: center;
}

.search-btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--accent-color) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.search-btn-primary:active {
  transform: translateY(0);
}

/* ========================================
   CARDS
   ======================================== */
.card {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-slow);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.card-title {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-2);
}

.card-footer {
  background-color: var(--neutral-color) !important;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: var(--space-4);
}

/* Recipe Cards */
.recipe-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-slow);
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.recipe-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  text-decoration: none;
  color: inherit;
}

.recipe-image {
  width: 100%;
  height: 180px;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  position: relative;
  overflow: hidden;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: saturate(1.2);
}

/* ========================================
   NAVIGATION COMPONENTS
   ======================================== */

/* Sidebar Navigation */
.sidebar {
  width: 70px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 0;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 1000;
  border-right: 1px solid #f0f0f0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.sidebar-logo {
  margin-bottom: 2rem;
  padding: 0;
}

.sidebar-logo-img {
  width: 40px;
  height: auto;
  max-width: 100%;
}

.sidebar-new-button {
  margin-bottom: 2rem;
  padding: 0;
}

.sidebar-new-link,
.sidebar-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 0;
  color: #9ca3af;
  text-decoration: none;
  transition: all var(--transition-base);
  border-radius: 8px;
  width: 50px;
  margin: 0 auto;
  position: relative;
}

.sidebar-new-link:hover {
  color: #6b7280;
  background-color: #f9fafb;
}

.sidebar-link:hover,
.sidebar-link.active {
  color: #10b981;
  background-color: #ecfdf5;
}

.sidebar-link.active::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background-color: #10b981;
  border-radius: 2px;
}

.sidebar-link i,
.sidebar-new-link i {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.sidebar-link span,
.sidebar-new-link span {
  font-size: 0.625rem;
  text-align: center;
  line-height: 1;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.sidebar-item {
  width: 100%;
  display: flex;
  justify-content: center;
}

/* Hide sidebar on mobile */
@media (max-width: 768px) {
  .sidebar {
    display: none;
  }
}

/* Mobile Bottom Navigation - Inspired by mobile-inspiration.html */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  z-index: 1000;
  transition: transform 0.3s ease;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
  display: none; /* Hidden by default */
}

.mobile-bottom-nav.hidden {
  transform: translateY(100%);
}

.mobile-nav-container {
  position: relative;
  width: 100%;
  max-width: 300px;
  height: 60px;
  background: var(--neutral-color);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.mobile-nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 20px;
  z-index: 2;
}

.mobile-nav-item.active {
  background: rgba(16, 185, 129, 0.1);
}

.mobile-nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  stroke: var(--text-light);
  fill: none;
  stroke-width: 2;
}

.mobile-nav-item.active .mobile-nav-icon {
  stroke: var(--primary-color);
}

.mobile-nav-label {
  font-size: 12px;
  color: var(--text-light);
  font-weight: 500;
  transition: all 0.3s ease;
}

.mobile-nav-item.active .mobile-nav-label {
  color: var(--primary-color);
}

/* New Button (Elevated) - Center button styling */
.mobile-nav-item.mobile-nav-new {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  transition: all 0.3s ease;
}

.mobile-nav-item.mobile-nav-new:hover {
  transform: translateX(-50%) translateY(-2px);
  box-shadow: 0 12px 30px rgba(16, 185, 129, 0.5);
}

.mobile-nav-item.mobile-nav-new .mobile-nav-icon {
  color: var(--text-on-primary);
  stroke: var(--text-on-primary);
  margin-bottom: 0;
}

.mobile-nav-item.mobile-nav-new .mobile-nav-label {
  color: var(--text-on-primary);
  font-size: 10px;
  margin-top: 2px;
}

/* Curved cutout effect for elevated button */
.mobile-nav-container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 40px;
  background: var(--neutral-color);
  border-radius: 0 0 40px 40px;
  z-index: 1;
}

.mobile-nav-container::after {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 50px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0 0 50px 50px;
  z-index: 0;
}

/* ========================================
   MOBILE RESPONSIVE ADJUSTMENTS
   ======================================== */
@media (max-width: 768px) {
  .sidebar {
    display: none;
  }

  .search-form-wrapper {
    flex-direction: column;
    gap: var(--space-3);
    padding: var(--space-4);
  }

  .search-type-select,
  .search-input,
  .search-btn-primary {
    width: 100%;
    min-height: 48px;
  }

  .search-btn-primary {
    font-size: var(--font-size-base);
    padding: var(--space-4) var(--space-5);
  }

  .btn {
    min-height: 44px;
    min-width: 44px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .mobile-bottom-nav {
    display: flex;
  }
}

@media (min-width: 769px) {
  .mobile-bottom-nav {
    display: none;
  }
}

/* Mobile Navigation Responsive Adjustments */
@media (max-width: 375px) {
  .mobile-nav-container {
    max-width: 280px;
  }

  .mobile-nav-item {
    padding: 6px 12px;
  }
}

/* ========================================
   FILTER COMPONENTS
   ======================================== */

/* Quick Filters Container */
.quick-filters {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 24px 0;
  width: 100%;
}

.quick-filters .btn-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

/* Quick Filter Buttons */
.quick-filters .btn {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 24px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all var(--transition-base);
  white-space: nowrap;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.quick-filters .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
  transition: left 0.5s ease;
}

.quick-filters .btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.quick-filters .btn:hover::before {
  left: 100%;
}

.quick-filters .btn.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.quick-filters .btn.active:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--accent-color) 100%);
  transform: translateY(-1px);
}

/* Advanced Filters Toggle */
.advanced-filters-toggle {
  margin: 16px 0;
  text-align: center; /* Center align on all screens */
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.advanced-filters-toggle .btn-link {
  background: #f9fafb;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  color: #6b7280;
  text-decoration: none;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all var(--transition-base);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 0 auto;
}

.advanced-filters-toggle .btn-link:hover {
  background: #f3f4f6;
  border-color: var(--primary-color);
  color: var(--primary-dark);
  transform: translateY(-1px);
}

.advanced-filters-toggle .btn-link:focus {
  background: #f3f4f6;
  border-color: var(--primary-color);
  color: var(--primary-dark);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  outline: none;
}

/* Ensure collapse is closed by default - more robust implementation */
.collapse:not(.show) {
  display: none !important;
}

.collapse.show {
  display: block !important;
}

/* Advanced filters specific collapse behavior */
#advancedFilters:not(.show) {
  display: none !important;
  height: 0 !important;
  overflow: hidden !important;
}

#advancedFilters.show {
  display: block !important;
  height: auto !important;
}

/* Ensure desktop filters are hidden on mobile */
@media (max-width: 767.98px) {
  #advancedFilters {
    display: none !important;
  }
}

/* Ensure mobile filters are hidden on desktop */
@media (min-width: 768px) {
  .mobile-filters-modal {
    display: none !important;
  }

  /* Ensure advanced filters are properly centered on desktop */
  #advanced-filters {
    text-align: center;
    width: 100%;
  }

  .advanced-filters-toggle {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .advanced-filters-toggle .btn-link {
    margin: 0 auto;
  }

  /* Center the collapsed content as well */
  #advancedFilters {
    text-align: left; /* Reset text alignment for content inside */
  }
}

/* Filter Sections */
.filter-section {
  background: #ffffff;
  border: 2px solid #f3f4f6;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-sm);
}

.filter-section h6 {
  color: var(--text-dark);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f3f4f6;
}

/* Form Controls */
.form-check {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-check-input {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  transition: all var(--transition-base);
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.form-check-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
}

.btn-check:checked + .btn-outline-secondary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* Mobile Filters Modal */
.mobile-filters-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mobile-filters-modal.show {
  display: flex;
  opacity: 1;
}

.mobile-filters-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.mobile-filters-content {
  position: relative;
  background: white;
  margin: auto;
  width: 95%;
  max-width: 500px;
  max-height: 90vh;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.mobile-filters-modal.show .mobile-filters-content {
  transform: translateY(0);
}

.mobile-filters-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 2px solid #f3f4f6;
  border-radius: 20px 20px 0 0;
  background: var(--neutral-color);
}

.mobile-filters-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-filters-close {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--text-light);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-filters-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-dark);
}

.mobile-filters-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
  -webkit-overflow-scrolling: touch;
}

.mobile-filter-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f3f4f6;
}

.mobile-filter-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.mobile-filter-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--primary-color);
}

.mobile-filter-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobile-filter-options .form-check {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.mobile-filter-options .form-check-input {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.mobile-filter-options .form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.mobile-filter-options .form-check-label {
  font-size: 16px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  flex: 1;
}

.mobile-btn-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-btn-group .btn {
  min-height: 48px;
  font-size: 16px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.mobile-select {
  min-height: 48px;
  font-size: 16px;
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.2s ease;
}

.mobile-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.mobile-filters-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 2px solid #f3f4f6;
  border-radius: 0 0 20px 20px;
  background: #f9fafb;
}

.mobile-filter-btn {
  flex: 1;
  min-height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.2s ease;
}

/* Mobile Filter Adjustments */
@media (max-width: 768px) {
  .quick-filters .btn {
    padding: 12px 24px; /* Larger mobile buttons */
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 44px;
  }

  .quick-filters {
    margin: 16px 0;
    padding: 0 var(--space-2);
  }
}

/* ========================================
   LOADING ANIMATIONS & HTMX INDICATORS
   ======================================== */

/* Basic HTMX indicator styles */
.htmx-indicator {
  display: none;
}

.htmx-request .htmx-indicator {
  display: inline-block;
}

.htmx-request.htmx-indicator {
  display: inline-block;
}

/* Search confirmation tooltip */
.search-confirmation-tooltip {
  animation: pulse 0.5s ease-in-out infinite alternate;
}

.search-confirmation-tooltip .tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #ffc107;
}

@keyframes pulse {
  from {
    transform: translateX(-50%) scale(1);
  }
  to {
    transform: translateX(-50%) scale(1.05);
  }
}

/* Food-Themed Loading Overlay */
.food-loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(2px);
  z-index: 2000;
  display: none;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-slow);
}

.food-loader-overlay.show {
  display: flex;
  opacity: 1;
}

/* Results area loading state */
.recipe-results-loading {
  position: relative;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 12px;
  margin: 20px 0;
}

/* Main food loader container */
.food-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 40px;
  text-align: center;
}

/* Cooking pot animation */
.cooking-pot-loader {
  position: relative;
  width: 80px;
  height: 80px;
}

.pot {
  width: 60px;
  height: 40px;
  background: linear-gradient(145deg, var(--secondary-color), var(--secondary-dark));
  border-radius: 0 0 30px 30px;
  position: relative;
  margin: 20px auto 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pot::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 6px;
  background: #333;
  border-radius: 3px;
}

.steam {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: steam 1.5s ease-in-out infinite;
}

.steam:nth-child(2) {
  left: 40%;
  animation-delay: 0.3s;
}

.steam:nth-child(3) {
  left: 60%;
  animation-delay: 0.6s;
}

@keyframes steam {
  0%, 100% {
    opacity: 0;
    transform: translateX(-50%) translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-10px);
  }
}

/* Inline loading indicators for search buttons */
.search-loading-icon {
  display: none;
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-loading-icon.active {
  display: inline-block;
}

.search-loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading text animations */
.loading-text {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}
