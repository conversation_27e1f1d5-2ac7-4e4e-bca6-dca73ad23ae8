<!-- Advanced Filters Button -->
<div class="advanced-filters-toggle mb-4">
    <!-- Desktop/Tablet: Collapse toggle -->
    <button class="btn btn-link d-none d-md-block" type="button"
            data-bs-toggle="collapse"
            data-bs-target="#advancedFilters"
            aria-expanded="false"
            aria-controls="advancedFilters"
            id="advancedFiltersToggle">
        <i class="fas fa-sliders-h"></i> Advanced Filters
    </button>

    <!-- Mobile: Modal toggle -->
    <button class="btn btn-link d-md-none" type="button" onclick="openMobileFiltersModal()">
        <i class="fas fa-sliders-h"></i> Advanced Filters
    </button>
</div>

<!-- Desktop/Tablet Advanced Filters Section (Collapse) -->
<div class="collapse d-none d-md-block" id="advancedFilters" aria-labelledby="advancedFiltersToggle">
    <div class="card card-body shadow-sm mb-4">
        <form id="advanced-filters-form"
              hx-get="{% url 'recipes:unified_search' %}"
              hx-target="#recipe-results"
              hx-trigger="submit"
              hx-indicator=".htmx-indicator"
              hx-push-url="true">

            <!-- Hidden input to maintain search context -->
            <input type="hidden" name="search_type" id="filter_search_type" value="">
            <input type="hidden" name="search_query" id="filter_search_query" value="">

            <div class="row g-4">
            <!-- Dietary Restrictions -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Dietary Restrictions</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="vegetarian" name="vegetarian">
                            <label class="form-check-label" for="vegetarian">Vegetarian</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="vegan" name="vegan">
                            <label class="form-check-label" for="vegan">Vegan</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="glutenFree" name="glutenFree">
                            <label class="form-check-label" for="glutenFree">Gluten-Free</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="dairyFree" name="dairyFree">
                            <label class="form-check-label" for="dairyFree">Dairy-Free</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="nutFree" name="nutFree">
                            <label class="form-check-label" for="nutFree">Nut-Free</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="lowCarb" name="lowCarb">
                            <label class="form-check-label" for="lowCarb">Low-Carb</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="keto" name="keto">
                            <label class="form-check-label" for="keto">Keto</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="paleo" name="paleo">
                            <label class="form-check-label" for="paleo">Paleo</label>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Main Ingredients -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Main Ingredients</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientRice">
                            <label class="form-check-label" for="ingredientRice">Rice</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientEggs">
                            <label class="form-check-label" for="ingredientEggs">Eggs</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientPotatoes">
                            <label class="form-check-label"
                                for="ingredientPotatoes">Potatoes</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientOnions">
                            <label class="form-check-label" for="ingredientOnions">Onions</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientTomatoes">
                            <label class="form-check-label"
                                for="ingredientTomatoes">Tomatoes</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientChicken">
                            <label class="form-check-label" for="ingredientChicken">Chicken</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientBeans">
                            <label class="form-check-label" for="ingredientBeans">Beans</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientPasta">
                            <label class="form-check-label" for="ingredientPasta">Pasta</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientBread">
                            <label class="form-check-label" for="ingredientBread">Bread</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientOil">
                            <label class="form-check-label" for="ingredientOil">Oil</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientSalt">
                            <label class="form-check-label" for="ingredientSalt">Salt</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ingredientPepper">
                            <label class="form-check-label" for="ingredientPepper">Pepper</label>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Cooking Time -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Cooking Time</h6>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="cookingTime" id="time1"
                            autocomplete="off" value="time1">
                        <label class="btn btn-outline-secondary" for="time1">Under 30m</label>
                        <input type="radio" class="btn-check" name="cookingTime" id="time2"
                            autocomplete="off" value="time2">
                        <label class="btn btn-outline-secondary" for="time2">30-60m</label>
                        <input type="radio" class="btn-check" name="cookingTime" id="time3"
                            autocomplete="off" value="time3">
                        <label class="btn btn-outline-secondary" for="time3">60m+</label>
                    </div>
                </div>
            </div>

            <!-- Cuisine Type -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Cuisine Type</h6>
                    <select class="form-select" name="cuisine_type">
                        <option value="">Select Cuisine</option>
                        <option value="nigerian">Nigerian</option>
                        <option value="west-african">West African</option>
                        <option value="east-african">East African</option>
                        <option value="caribbean">Caribbean</option>
                        <option value="afro-fusion">Afro-fusion</option>
                    </select>
                </div>
            </div>

            <!-- Budget Range -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Budget Range</h6>
                    <select class="form-select" name="budget_range">
                        <option value="">Select Budget</option>
                        <option value="budget">Budget (₦200-₦500)</option>
                        <option value="moderate">Moderate (₦500-₦1000)</option>
                        <option value="premium">Premium (₦1000+)</option>
                    </select>
                </div>
            </div>

            <!-- Meal Type -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Meal Type</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="breakfast" name="breakfast">
                            <label class="form-check-label" for="breakfast">Breakfast</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="lunch" name="lunch">
                            <label class="form-check-label" for="lunch">Lunch</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="dinner" name="dinner">
                            <label class="form-check-label" for="dinner">Dinner</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="snacks" name="snacks">
                            <label class="form-check-label" for="snacks">Snacks</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Required Tools -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Required Tools</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="stove">
                            <label class="form-check-label" for="stove">Stove</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="oven">
                            <label class="form-check-label" for="oven">Oven</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="blender">
                            <label class="form-check-label" for="blender">Blender</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="noCook">
                            <label class="form-check-label" for="noCook">No-cook</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seasonal / Weather Friendly -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Seasonal / Weather Friendly</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="coldWeather">
                            <label class="form-check-label" for="coldWeather">Cold Weather</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="hotWeather">
                            <label class="form-check-label" for="hotWeather">Hot Weather</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Skill Level -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Skill Level</h6>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="skillLevel" id="skillBeginner"
                            autocomplete="off" value="beginner">
                        <label class="btn btn-outline-secondary"
                            for="skillBeginner">Beginner</label>
                        <input type="radio" class="btn-check" name="skillLevel"
                            id="skillIntermediate" autocomplete="off" value="intermediate">
                        <label class="btn btn-outline-secondary"
                            for="skillIntermediate">Intermediate</label>
                        <input type="radio" class="btn-check" name="skillLevel" id="skillAdvanced"
                            autocomplete="off" value="advanced">
                        <label class="btn btn-outline-secondary"
                            for="skillAdvanced">Advanced</label>
                    </div>
                </div>
            </div>

            <!-- Nutritional Filters -->
            <div class="col-md-6">
                <div class="filter-section">
                    <h6 class="mb-3">Nutritional Filters</h6>

                    <!-- Calories Range -->
                    <div class="mb-3">
                        <label class="form-label small">Calories per Serving</label>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="min_calories" placeholder="Min" min="0" max="2000">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="max_calories" placeholder="Max" min="0" max="2000">
                            </div>
                        </div>
                    </div>

                    <!-- Protein Range -->
                    <div class="mb-3">
                        <label class="form-label small">Protein (g)</label>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="min_protein" placeholder="Min" min="0" max="200">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="max_protein" placeholder="Max" min="0" max="200">
                            </div>
                        </div>
                    </div>

                    <!-- Carbs Range -->
                    <div class="mb-2">
                        <label class="form-label small">Carbohydrates (g)</label>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="min_carbs" placeholder="Min" min="0" max="300">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="max_carbs" placeholder="Max" min="0" max="300">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Action Buttons -->
            <div class="col-12">
                <div class="d-flex justify-content-center gap-3 mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Apply Filters
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearAdvancedFilters()">
                        <i class="fas fa-times"></i> Clear All
                    </button>
                </div>
            </div>
        </div>
        </form>
    </div>
</div>

<!-- Mobile Advanced Filters Modal -->
<div class="mobile-filters-modal" id="mobileFiltersModal">
    <div class="mobile-filters-overlay" onclick="closeMobileFiltersModal()"></div>
    <div class="mobile-filters-content">
        <!-- Modal Header -->
        <div class="mobile-filters-header">
            <h5 class="mobile-filters-title">
                <i class="fas fa-sliders-h"></i> Advanced Filters
            </h5>
            <button class="mobile-filters-close" onclick="closeMobileFiltersModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Modal Body (Scrollable) -->
        <div class="mobile-filters-body">
            <form id="mobile-advanced-filters-form"
                  hx-get="{% url 'recipes:unified_search' %}"
                  hx-target="#recipe-results"
                  hx-trigger="submit"
                  hx-indicator=".htmx-indicator"
                  hx-push-url="true">

                <!-- Hidden input to maintain search context -->
                <input type="hidden" name="search_type" id="mobile_filter_search_type" value="">
                <input type="hidden" name="search_query" id="mobile_filter_search_query" value="">
            <!-- Dietary Restrictions -->
            <div class="mobile-filter-section">
                <h6 class="mobile-filter-title">Dietary Restrictions</h6>
                <div class="mobile-filter-options">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-vegetarian" name="vegetarian">
                        <label class="form-check-label" for="mobile-vegetarian">Vegetarian</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-vegan" name="vegan">
                        <label class="form-check-label" for="mobile-vegan">Vegan</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-glutenFree" name="glutenFree">
                        <label class="form-check-label" for="mobile-glutenFree">Gluten-Free</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-dairyFree" name="dairyFree">
                        <label class="form-check-label" for="mobile-dairyFree">Dairy-Free</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-nutFree" name="nutFree">
                        <label class="form-check-label" for="mobile-nutFree">Nut-Free</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-keto" name="keto">
                        <label class="form-check-label" for="mobile-keto">Keto</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-paleo" name="paleo">
                        <label class="form-check-label" for="mobile-paleo">Paleo</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-lowCarb" name="lowCarb">
                        <label class="form-check-label" for="mobile-lowCarb">Low-Carb</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-paleo">
                        <label class="form-check-label" for="mobile-paleo">Paleo</label>
                    </div>
                </div>
            </div>

            <!-- Cooking Time -->
            <div class="mobile-filter-section">
                <h6 class="mobile-filter-title">Cooking Time</h6>
                <div class="mobile-filter-options">
                    <div class="mobile-btn-group">
                        <input type="radio" class="btn-check" name="cookingTime" id="mobile-time1" autocomplete="off" value="time1">
                        <label class="btn btn-outline-secondary" for="mobile-time1">Under 30m</label>
                        <input type="radio" class="btn-check" name="cookingTime" id="mobile-time2" autocomplete="off" value="time2">
                        <label class="btn btn-outline-secondary" for="mobile-time2">30-60m</label>
                        <input type="radio" class="btn-check" name="cookingTime" id="mobile-time3" autocomplete="off" value="time3">
                        <label class="btn btn-outline-secondary" for="mobile-time3">60m+</label>
                    </div>
                </div>
            </div>

            <!-- Cuisine Type -->
            <div class="mobile-filter-section">
                <h6 class="mobile-filter-title">Cuisine Type</h6>
                <div class="mobile-filter-options">
                    <select class="form-select mobile-select" name="cuisine_type">
                        <option value="">Select Cuisine</option>
                        <option value="nigerian">Nigerian</option>
                        <option value="west-african">West African</option>
                        <option value="east-african">East African</option>
                        <option value="caribbean">Caribbean</option>
                        <option value="afro-fusion">Afro-fusion</option>
                    </select>
                </div>
            </div>

            <!-- Meal Type -->
            <div class="mobile-filter-section">
                <h6 class="mobile-filter-title">Meal Type</h6>
                <div class="mobile-filter-options">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-breakfast" name="breakfast">
                        <label class="form-check-label" for="mobile-breakfast">Breakfast</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-lunch" name="lunch">
                        <label class="form-check-label" for="mobile-lunch">Lunch</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-dinner" name="dinner">
                        <label class="form-check-label" for="mobile-dinner">Dinner</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-snacks" name="snacks">
                        <label class="form-check-label" for="mobile-snacks">Snacks</label>
                    </div>
                </div>
            </div>

            <!-- Skill Level -->
            <div class="mobile-filter-section">
                <h6 class="mobile-filter-title">Skill Level</h6>
                <div class="mobile-filter-options">
                    <div class="mobile-btn-group">
                        <input type="radio" class="btn-check" name="skillLevel" id="mobile-skillBeginner" autocomplete="off" value="beginner">
                        <label class="btn btn-outline-secondary" for="mobile-skillBeginner">Beginner</label>
                        <input type="radio" class="btn-check" name="skillLevel" id="mobile-skillIntermediate" autocomplete="off" value="intermediate">
                        <label class="btn btn-outline-secondary" for="mobile-skillIntermediate">Intermediate</label>
                        <input type="radio" class="btn-check" name="skillLevel" id="mobile-skillAdvanced" autocomplete="off" value="advanced">
                        <label class="btn btn-outline-secondary" for="mobile-skillAdvanced">Advanced</label>
                    </div>
                </div>
            </div>

            <!-- Nutritional Filters -->
            <div class="mobile-filter-section">
                <h6 class="mobile-filter-title">Nutritional Filters</h6>
                <div class="mobile-filter-options">

                    <!-- Calories Range -->
                    <div class="mb-3">
                        <label class="form-label small">Calories per Serving</label>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="mobile_min_calories" placeholder="Min" min="0" max="2000">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="mobile_max_calories" placeholder="Max" min="0" max="2000">
                            </div>
                        </div>
                    </div>

                    <!-- Protein Range -->
                    <div class="mb-3">
                        <label class="form-label small">Protein (g)</label>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="mobile_min_protein" placeholder="Min" min="0" max="200">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="mobile_max_protein" placeholder="Max" min="0" max="200">
                            </div>
                        </div>
                    </div>

                    <!-- Carbs Range -->
                    <div class="mb-2">
                        <label class="form-label small">Carbohydrates (g)</label>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="mobile_min_carbs" placeholder="Min" min="0" max="300">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" name="mobile_max_carbs" placeholder="Max" min="0" max="300">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seasonal / Weather Friendly -->
            <div class="mobile-filter-section">
                <h6 class="mobile-filter-title">Seasonal / Weather Friendly</h6>
                <div class="mobile-filter-options">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-coldWeather">
                        <label class="form-check-label" for="mobile-coldWeather">Cold Weather</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobile-hotWeather">
                        <label class="form-check-label" for="mobile-hotWeather">Hot Weather</label>
                    </div>
                </div>
            </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="mobile-filters-footer">
            <button type="button" class="btn btn-outline-secondary mobile-filter-btn" onclick="clearMobileFilters()">
                <i class="fas fa-times"></i> Clear All
            </button>
            <button type="button" class="btn btn-primary mobile-filter-btn" onclick="applyMobileFilters()">
                <i class="fas fa-search"></i> Apply Filters
            </button>
        </div>
    </div>
</div>

<script>
// Advanced Filters JavaScript Functions

function clearAdvancedFilters() {
    // Clear all form inputs in desktop advanced filters
    const form = document.getElementById('advanced-filters-form');
    if (form) {
        form.reset();
        // Clear any checked checkboxes and radio buttons
        form.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(input => {
            input.checked = false;
        });
        // Clear select dropdowns
        form.querySelectorAll('select').forEach(select => {
            select.selectedIndex = 0;
        });
        // Clear number inputs
        form.querySelectorAll('input[type="number"]').forEach(input => {
            input.value = '';
        });
    }
}

// Advanced Filters Desktop Collapse Management
function initializeAdvancedFiltersCollapse() {
    const advancedFiltersToggle = document.getElementById('advancedFiltersToggle');
    const advancedFiltersCollapse = document.getElementById('advancedFilters');

    if (advancedFiltersToggle && advancedFiltersCollapse) {
        // Ensure collapse is closed by default - remove any existing 'show' class
        advancedFiltersCollapse.classList.remove('show');
        advancedFiltersToggle.setAttribute('aria-expanded', 'false');

        // Remove any existing event listeners to prevent duplicates
        const newToggle = advancedFiltersToggle.cloneNode(true);
        advancedFiltersToggle.parentNode.replaceChild(newToggle, advancedFiltersToggle);

        // Add event listeners for collapse state changes
        advancedFiltersCollapse.addEventListener('show.bs.collapse', function() {
            newToggle.setAttribute('aria-expanded', 'true');
            const icon = newToggle.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-sliders-h');
                icon.classList.add('fa-times');
            }
        });

        advancedFiltersCollapse.addEventListener('hide.bs.collapse', function() {
            newToggle.setAttribute('aria-expanded', 'false');
            const icon = newToggle.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-sliders-h');
            }
        });

        // Force close on initialization - use a more robust approach
        if (window.bootstrap && window.bootstrap.Collapse) {
            // Destroy any existing collapse instance
            const existingInstance = bootstrap.Collapse.getInstance(advancedFiltersCollapse);
            if (existingInstance) {
                existingInstance.dispose();
            }

            // Create new collapse instance with toggle: false to prevent auto-opening
            const bsCollapse = new bootstrap.Collapse(advancedFiltersCollapse, {
                toggle: false
            });

            // Ensure it's hidden
            bsCollapse.hide();
        }

        console.log('Advanced filters collapse initialized - should be closed by default');
    }
}

function clearMobileFilters() {
    // Clear all form inputs in mobile advanced filters
    const form = document.getElementById('mobile-advanced-filters-form');
    if (form) {
        form.reset();
        // Clear any checked checkboxes and radio buttons
        form.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(input => {
            input.checked = false;
        });
        // Clear select dropdowns
        form.querySelectorAll('select').forEach(select => {
            select.selectedIndex = 0;
        });
        // Clear number inputs
        form.querySelectorAll('input[type="number"]').forEach(input => {
            input.value = '';
        });
    }
}

function applyMobileFilters() {
    // Get the current search context from the main search form
    const mainSearchForm = document.querySelector('#main-search-box form');
    if (mainSearchForm) {
        const searchType = mainSearchForm.querySelector('[name="search_type"]')?.value || 'ingredients';
        const searchQuery = mainSearchForm.querySelector('[name="search_query"]')?.value || '';

        // Update hidden fields in mobile form
        document.getElementById('mobile_filter_search_type').value = searchType;
        document.getElementById('mobile_filter_search_query').value = searchQuery;
    }

    // Submit the mobile form
    const mobileForm = document.getElementById('mobile-advanced-filters-form');
    if (mobileForm) {
        mobileForm.dispatchEvent(new Event('submit'));
        // Close the modal
        closeMobileFiltersModal();
    }
}

// Initialize advanced filters on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the collapse functionality
    initializeAdvancedFiltersCollapse();

    // Set up form submission handler
    const desktopForm = document.getElementById('advanced-filters-form');
    if (desktopForm) {
        desktopForm.addEventListener('submit', function(e) {
            // Get the current search context from the main search form
            const mainSearchForm = document.querySelector('#main-search-box form');
            if (mainSearchForm) {
                const searchType = mainSearchForm.querySelector('[name="search_type"]')?.value || 'ingredients';
                const searchQuery = mainSearchForm.querySelector('[name="search_query"]')?.value || '';

                // Update hidden fields
                document.getElementById('filter_search_type').value = searchType;
                document.getElementById('filter_search_query').value = searchQuery;
            }
        });
    }
});

// Re-initialize after HTMX content swaps to ensure filters stay closed
document.body.addEventListener('htmx:afterSwap', function(event) {
    // Only re-initialize if the swap affects the search results area
    if (event.detail.target && event.detail.target.id === 'recipe-results') {
        // Small delay to ensure DOM is fully updated
        setTimeout(function() {
            initializeAdvancedFiltersCollapse();
        }, 100);
    }
});

// Handle HTMX before request to ensure filters stay closed
document.body.addEventListener('htmx:beforeRequest', function(event) {
    // Ensure advanced filters are closed before any HTMX request
    const advancedFiltersCollapse = document.getElementById('advancedFilters');
    if (advancedFiltersCollapse && advancedFiltersCollapse.classList.contains('show')) {
        // Only close if it's currently open and the request is not from the filters themselves
        const isFilterRequest = event.detail.elt && (
            event.detail.elt.id === 'advanced-filters-form' ||
            event.detail.elt.closest('#advanced-filters-form')
        );

        if (!isFilterRequest) {
            const bsCollapse = bootstrap.Collapse.getInstance(advancedFiltersCollapse);
            if (bsCollapse) {
                bsCollapse.hide();
            }
        }
    }
});

// Also handle page navigation and history changes
window.addEventListener('popstate', function(event) {
    setTimeout(function() {
        initializeAdvancedFiltersCollapse();
    }, 100);
});

// Handle window resize to ensure proper behavior on screen size changes
window.addEventListener('resize', function() {
    // Debounce resize events
    clearTimeout(window.resizeTimeout);
    window.resizeTimeout = setTimeout(function() {
        initializeAdvancedFiltersCollapse();
    }, 250);
});

// Mobile filters modal functions (if not already defined)
function openMobileFiltersModal() {
    document.getElementById('mobileFiltersModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeMobileFiltersModal() {
    document.getElementById('mobileFiltersModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}
</script>