"""
Central API manager for Recipe finder.
Handles all API calls, fallback logic, normalization, and caching.
"""

from typing import List, Dict, Any, Optional
import logging
import os
import json
import hashlib
from dotenv import load_dotenv
from django.core.cache import cache
from .cache_utils import (
    safe_cache_get,
    safe_cache_set,
    safe_cache_delete,
    RECIPE_CACHE_DURATION,
    API_RATE_LIMIT_CACHE_DURATION
)

# Load environment variables from .env file
load_dotenv()

SPOONACULAR_API_KEY = os.getenv("SPOONACULAR_API_KEY")
TASTY_API_HOST = os.getenv("TASTY_API_HOST")
TASTY_API_KEY = os.getenv("TASTY_API_KEY")
FOODDATA_API_KEY = os.getenv("FOODDATA_API_KEY")

# TheMealDB API (free, no key required)
THEMEALDB_BASE_URL = "https://www.themealdb.com/api/json/v1/1"

# Request counting for Tasty API (limit to 10 per day)
TASTY_REQUEST_COUNT_KEY = "tasty_api_requests_today"
TASTY_DAILY_LIMIT = 10

# Cache duration constants are now imported from cache_utils

def _generate_cache_key(prefix: str, query: str, filters: Optional[Dict[str, Any]] = None) -> str:
    """
    Generate a consistent cache key with sorted JSON serialization for filters.

    Args:
        prefix: Cache key prefix (e.g., 'search_recipes', 'recipe_details')
        query: Main query parameter
        filters: Optional filters dictionary

    Returns:
        Consistent cache key string
    """
    if filters:
        # Sort keys to ensure consistent serialization
        filters_key = json.dumps(filters, sort_keys=True)
    else:
        filters_key = "none"

    return f"{prefix}:{query}:{filters_key}"

# Rate limiting configuration
RATE_LIMIT_DELAYS = {
    'spoonacular': 0.1,  # 100ms between requests
    'tasty': 0.2,        # 200ms between requests
    'fooddata': 0.15,    # 150ms between requests
    'themealdb': 0.05,   # 50ms between requests (free API)
}

# Retry configuration
MAX_RETRIES = 3
BASE_RETRY_DELAY = 1  # seconds

# Debug: Log API config (remove this in production)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

logger.debug(f"TASTY_API_HOST: {'Set' if TASTY_API_HOST else 'Not set'}")
logger.debug(f"TASTY_API_KEY: {'Set' if TASTY_API_KEY else 'Not set'}")
logger.debug(f"SPOONACULAR_API_KEY: {'Set' if SPOONACULAR_API_KEY else 'Not set'}")


# --- Main API Manager Functions ---
import requests
import time
import random
from requests.exceptions import HTTPError

def _make_api_request(url: str, api_name: str, headers: Optional[Dict[str, str]] = None,
                     params: Optional[Dict[str, Any]] = None, timeout: int = 10) -> requests.Response:
    """
    Make an API request with rate limiting, retry logic, and 429 handling.

    Args:
        url: The API endpoint URL
        api_name: Name of the API for rate limiting ('spoonacular', 'tasty', 'fooddata', 'themealdb')
        headers: Optional request headers
        params: Optional request parameters
        timeout: Request timeout in seconds

    Returns:
        requests.Response object

    Raises:
        HTTPError: If all retries fail
    """
    # Apply rate limiting delay
    if api_name in RATE_LIMIT_DELAYS:
        time.sleep(RATE_LIMIT_DELAYS[api_name])

    for attempt in range(MAX_RETRIES + 1):
        try:
            logger.debug(f"Making {api_name} API request (attempt {attempt + 1}): {url}")
            response = requests.get(url, headers=headers, params=params, timeout=timeout)

            # Handle rate limiting (429 status code)
            if response.status_code == 429:
                if attempt < MAX_RETRIES:
                    # Exponential backoff with jitter
                    delay = BASE_RETRY_DELAY * (2 ** attempt) + random.uniform(0, 1)
                    logger.warning(f"{api_name} API rate limited (429). Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"{api_name} API rate limited after {MAX_RETRIES} retries")
                    response.raise_for_status()

            # Handle other HTTP errors
            response.raise_for_status()
            return response

        except HTTPError as e:
            if attempt < MAX_RETRIES and e.response.status_code in [429, 500, 502, 503, 504]:
                # Retry on rate limiting and server errors
                delay = BASE_RETRY_DELAY * (2 ** attempt) + random.uniform(0, 1)
                logger.warning(f"{api_name} API error {e.response.status_code}. Retrying in {delay:.2f} seconds...")
                time.sleep(delay)
                continue
            else:
                logger.error(f"{api_name} API request failed after {attempt + 1} attempts: {e}")
                raise
        except Exception as e:
            if attempt < MAX_RETRIES:
                delay = BASE_RETRY_DELAY * (2 ** attempt) + random.uniform(0, 1)
                logger.warning(f"{api_name} API request failed. Retrying in {delay:.2f} seconds... Error: {e}")
                time.sleep(delay)
                continue
            else:
                logger.error(f"{api_name} API request failed after {MAX_RETRIES + 1} attempts: {e}")
                raise




def search_recipes(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes using Tasty API by default.
    Falls back to Spoonacular, then TheMealDB if both fail.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_recipes", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    # Try Tasty API first
    try:
        results = _tasty_search_recipes(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logging.error(f"Tasty API search failed: {e}")

    # Fallback to Spoonacular
    try:
        results = _spoonacular_search_recipes(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logging.error(f"Spoonacular API search failed: {e}")
        # Check if it's a payment error (402) and log appropriately
        if hasattr(e, 'response') and e.response.status_code == 402:
            logging.warning("Spoonacular API quota exceeded (402). Falling back to TheMealDB.")

    # Final fallback to TheMealDB (free API)
    try:
        results = _themealdb_search_by_name(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logging.error(f"TheMealDB API search failed: {e}")

    return []

def get_recipe_details(recipe_id: str, source: str = 'tasty') -> Dict[str, Any]:
    """
    Fetch recipe details (ingredients, instructions, image, etc.) from the given source.
    Falls back to other sources if the primary source fails.
    Returns standardized recipe details with robust Redis caching.
    """
    cache_key = f"get_recipe_details:{recipe_id}:{source}"
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        if source == 'tasty':
            details = _tasty_get_recipe_details(recipe_id)
            if details:
                safe_cache_set(cache_key, details, timeout=RECIPE_CACHE_DURATION)
                return details
        elif source == 'themealdb':
            details = _themealdb_get_recipe_details(recipe_id)
            if details:
                safe_cache_set(cache_key, details, timeout=RECIPE_CACHE_DURATION)
                return details
        elif source == 'spoonacular':
            details = _spoonacular_get_recipe_details(recipe_id)
            if details:
                safe_cache_set(cache_key, details, timeout=RECIPE_CACHE_DURATION)
                return details
    except Exception as e:
        logger.error(f"{source.title()} API get details failed: {e}")

    # Fallback to other sources if primary source fails
    if source != 'spoonacular':
        try:
            details = _spoonacular_get_recipe_details(recipe_id)
            if details:
                safe_cache_set(cache_key, details, timeout=RECIPE_CACHE_DURATION)
                return details
        except Exception as e:
            logger.error(f"Spoonacular API fallback failed: {e}")

    return {}

# --- Request Counting for Tasty API ---
def _can_use_tasty_api() -> bool:
    """Check if we can still use Tasty API today (under daily limit)."""
    from datetime import date
    today = date.today().isoformat()
    cache_key = f"{TASTY_REQUEST_COUNT_KEY}:{today}"
    current_count = cache.get(cache_key, 0)
    return current_count < TASTY_DAILY_LIMIT

def _increment_tasty_api_count():
    """Increment the Tasty API request count for today."""
    from datetime import date
    today = date.today().isoformat()
    cache_key = f"{TASTY_REQUEST_COUNT_KEY}:{today}"
    current_count = cache.get(cache_key, 0)
    cache.set(cache_key, current_count + 1, timeout=API_RATE_LIMIT_CACHE_DURATION)
    logger.debug(f"Tasty API requests today: {current_count + 1}/{TASTY_DAILY_LIMIT}")

# --- Internal Helper Functions for Tasty API ---
def _tasty_search_recipes(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Calls the Tasty API to search for recipes by query and optional filters.
    For African cuisine, post-filters the results to only include African recipes.
    Returns a list of normalized recipe dicts.
    """
    try:
        if not TASTY_API_HOST or not TASTY_API_KEY:
            logger.error("Tasty API credentials not configured")
            return []

        # Check if we can use Tasty API today
        if not _can_use_tasty_api():
            logger.warning(f"Tasty API daily limit ({TASTY_DAILY_LIMIT}) reached, skipping")
            return []

        url = f"https://{TASTY_API_HOST}/recipes/list"
        headers = {
            "x-rapidapi-host": TASTY_API_HOST,
            "x-rapidapi-key": TASTY_API_KEY,
        }
        params = {
            "q": query,
            "from": 0,
            "size": 40,  # Increased size since we'll filter some out
        }
            
        logger.debug(f"Calling Tasty API: {url} with params: {params}")
        response = _make_api_request(url, 'tasty', headers=headers, params=params, timeout=10)

        # Increment request count after successful request
        _increment_tasty_api_count()

        data = response.json()
        
        logger.debug(f"Tasty API response status: {response.status_code}")
        logger.debug(f"Tasty API response data keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")
        
        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Tasty API")

        # Filter for African cuisine if specified
        if filters and filters.get('cuisine') == 'african':
            african_keywords = ['african', 'ethiopian', 'nigerian', 'moroccan', 'egyptian', 'kenyan', 
                              'south african', 'west african', 'east african', 'north african', 
                              'central african', 'maghreb', 'suya', 'jollof', 'fufu', 'injera']
            
            filtered_recipes = []
            for recipe in recipes:
                # Check recipe tags
                tags = [t.get('display_name', '').lower() for t in recipe.get('tags', [])]
                # Check recipe name and description
                name = recipe.get('name', '').lower()
                description = recipe.get('description', '').lower()
                
                # Check if any African keywords are present in tags, name, or description
                is_african = any(kw in ' '.join(tags + [name, description]) for kw in african_keywords)
                if is_african:
                    filtered_recipes.append(recipe)
            
            recipes = filtered_recipes[:20]  # Limit to 20 recipes after filtering
        else:
            # For global cuisine, just return first 20 recipes
            recipes = recipes[:20]
        
        return [_normalize_tasty_recipe(r) for r in recipes]
        
    except Exception as e:
        logger.error(f"Error in _tasty_search_recipes: {str(e)}", exc_info=True)
        raise

# --- Internal Helper Functions for Spoonacular API ---
def _spoonacular_search_recipes(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Calls the Spoonacular API to search for recipes by query and optional filters.
    Handles African vs Global cuisine filtering.
    Returns a list of normalized recipe dicts.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []
            
        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "query": query,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Set cuisine filter
        if filters and filters.get('cuisine') == 'african':
            params['cuisine'] = 'african'
        else:
            # For global cuisine, exclude African to avoid duplication
            params['excludeCuisine'] = 'african'
            
        logger.debug(f"Calling Spoonacular API: {url} with params: {params}")
        response = _make_api_request(url, 'spoonacular', params=params, timeout=10)
        data = response.json()
        
        logger.debug(f"Spoonacular API response status: {response.status_code}")
        logger.debug(f"Spoonacular API response data keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")
        
        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular API")
        
        return [_normalize_spoonacular_recipe(r) for r in recipes]
        
    except Exception as e:
        logger.error(f"Error in _spoonacular_search_recipes: {str(e)}", exc_info=True)
        raise

def _spoonacular_get_recipe_details(recipe_id: str) -> Dict[str, Any]:
    """
    Calls the Spoonacular API to get full details for a single recipe by ID.
    Returns a normalized recipe dict.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return {}
            
        url = f"https://api.spoonacular.com/recipes/{recipe_id}/information"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "includeNutrition": True,
        }
        logger.debug(f"Calling Spoonacular API: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        logger.debug(f"Spoonacular API response status: {response.status_code}")
        logger.debug(f"Spoonacular API response data keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")
        
        return _normalize_spoonacular_recipe(data)
        
    except Exception as e:
        logger.error(f"Error in _spoonacular_get_recipe_details: {str(e)}", exc_info=True)
        raise

def _spoonacular_search_by_name(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by name using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "query": query,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Apply additional filters if provided
        if filters:
            if filters.get('cuisine'):
                params['cuisine'] = filters['cuisine']
            if filters.get('diet'):
                params['diet'] = filters['diet']
            if filters.get('intolerances'):
                params['intolerances'] = filters['intolerances']
            if filters.get('maxReadyTime'):
                params['maxReadyTime'] = filters['maxReadyTime']
            if filters.get('number'):
                params['number'] = filters['number']
            if filters.get('sort'):
                params['sort'] = filters['sort']

        logger.debug(f"Calling Spoonacular API for name search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular name search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_name: {str(e)}", exc_info=True)
        raise

def _spoonacular_search_by_cuisine(cuisine: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by cuisine using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "cuisine": cuisine,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Apply additional filters if provided
        if filters:
            if filters.get('query'):
                params['query'] = filters['query']
            if filters.get('diet'):
                params['diet'] = filters['diet']
            if filters.get('intolerances'):
                params['intolerances'] = filters['intolerances']
            if filters.get('maxReadyTime'):
                params['maxReadyTime'] = filters['maxReadyTime']

        logger.debug(f"Calling Spoonacular API for cuisine search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular cuisine search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_cuisine: {str(e)}", exc_info=True)
        raise

def _spoonacular_search_by_diet(diet_restrictions: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by dietary restrictions using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Parse diet restrictions - could be diet type or intolerances
        # Common diets: vegetarian, vegan, pescetarian, paleo, ketogenic, etc.
        # Common intolerances: gluten, dairy, egg, soy, etc.
        diet_keywords = ['vegetarian', 'vegan', 'pescetarian', 'paleo', 'ketogenic', 'whole30', 'primal']
        intolerance_keywords = ['gluten', 'dairy', 'egg', 'soy', 'wheat', 'peanut', 'tree nut', 'shellfish', 'sesame', 'sulfite']

        diet_restrictions_lower = diet_restrictions.lower()

        # Check if it's a diet type
        for diet in diet_keywords:
            if diet in diet_restrictions_lower:
                params['diet'] = diet
                break

        # Check if it's an intolerance
        found_intolerances = []
        for intolerance in intolerance_keywords:
            if intolerance in diet_restrictions_lower:
                found_intolerances.append(intolerance)

        if found_intolerances:
            params['intolerances'] = ','.join(found_intolerances)

        # If no specific diet or intolerance found, use as general query
        if 'diet' not in params and 'intolerances' not in params:
            params['query'] = diet_restrictions

        # Apply additional filters if provided
        if filters:
            if filters.get('query'):
                params['query'] = filters['query']
            if filters.get('cuisine'):
                params['cuisine'] = filters['cuisine']
            if filters.get('maxReadyTime'):
                params['maxReadyTime'] = filters['maxReadyTime']

        logger.debug(f"Calling Spoonacular API for diet search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular diet search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_diet: {str(e)}", exc_info=True)
        raise

def _spoonacular_search_by_time(max_time: int, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by cooking time using Spoonacular complexSearch endpoint.
    """
    try:
        if not SPOONACULAR_API_KEY:
            logger.error("Spoonacular API key not configured")
            return []

        url = "https://api.spoonacular.com/recipes/complexSearch"
        params = {
            "apiKey": SPOONACULAR_API_KEY,
            "maxReadyTime": max_time,
            "number": 20,
            "addRecipeInformation": True,
        }

        # Apply additional filters if provided
        if filters:
            if filters.get('query'):
                params['query'] = filters['query']
            if filters.get('cuisine'):
                params['cuisine'] = filters['cuisine']
            if filters.get('diet'):
                params['diet'] = filters['diet']
            if filters.get('intolerances'):
                params['intolerances'] = filters['intolerances']

        logger.debug(f"Calling Spoonacular API for time search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        recipes = data.get("results", [])
        logger.debug(f"Found {len(recipes)} recipes from Spoonacular time search")

        return [_normalize_spoonacular_recipe(r) for r in recipes]

    except Exception as e:
        logger.error(f"Error in _spoonacular_search_by_time: {str(e)}", exc_info=True)
        raise

# --- Internal Helper Functions for TheMealDB API ---
def _themealdb_search_by_name(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by name using TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/search.php"
        params = {"s": query}

        logger.debug(f"Calling TheMealDB API for name search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        meals = data.get("meals", []) or []  # Handle null response
        logger.debug(f"Found {len(meals)} meals from TheMealDB name search")

        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_search_by_name: {str(e)}", exc_info=True)
        raise

def _themealdb_search_by_cuisine(area: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by area/cuisine using TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/filter.php"
        params = {"a": area}

        logger.debug(f"Calling TheMealDB API for area search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        meals = data.get("meals", []) or []
        logger.debug(f"Found {len(meals)} meals from TheMealDB area search")

        # Note: Filter endpoint returns limited data, so we need to fetch full details
        # For now, return the basic data and enhance later if needed
        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_search_by_cuisine: {str(e)}", exc_info=True)
        raise

def _themealdb_search_by_category(category: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by category using TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/filter.php"
        params = {"c": category}

        logger.debug(f"Calling TheMealDB API for category search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        meals = data.get("meals", []) or []
        logger.debug(f"Found {len(meals)} meals from TheMealDB category search")

        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_search_by_category: {str(e)}", exc_info=True)
        raise

def _themealdb_search_by_ingredient(ingredient: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search recipes by main ingredient using TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/filter.php"
        params = {"i": ingredient}

        logger.debug(f"Calling TheMealDB API for ingredient search: {url} with params: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        meals = data.get("meals", []) or []
        logger.debug(f"Found {len(meals)} meals from TheMealDB ingredient search")

        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_search_by_ingredient: {str(e)}", exc_info=True)
        raise

def _themealdb_get_random_meals(count: int = 1) -> List[Dict[str, Any]]:
    """
    Get random meals from TheMealDB API.
    """
    try:
        meals = []
        for _ in range(min(count, 10)):  # Limit to 10 to avoid too many requests
            url = f"{THEMEALDB_BASE_URL}/random.php"

            logger.debug(f"Calling TheMealDB API for random meal: {url}")
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()

            meal_data = data.get("meals", [])
            if meal_data:
                meals.extend(meal_data)

        logger.debug(f"Found {len(meals)} random meals from TheMealDB")
        return [_normalize_themealdb_recipe(meal) for meal in meals]

    except Exception as e:
        logger.error(f"Error in _themealdb_get_random_meals: {str(e)}", exc_info=True)
        raise

def _themealdb_get_recipe_details(recipe_id: str) -> Dict[str, Any]:
    """
    Get recipe details by ID from TheMealDB API.
    """
    try:
        url = f"{THEMEALDB_BASE_URL}/lookup.php"
        params = {"i": recipe_id}

        logger.debug(f"Calling TheMealDB API for recipe details: {url} with params: {params}")
        response = _make_api_request(url, 'themealdb', params=params, timeout=10)
        data = response.json()

        meals = data.get("meals", [])
        if meals:
            logger.debug(f"Found recipe details for ID {recipe_id} from TheMealDB")
            return _normalize_themealdb_recipe(meals[0])
        else:
            logger.warning(f"No recipe found for ID {recipe_id} in TheMealDB")
            return {}

    except Exception as e:
        logger.error(f"Error in _themealdb_get_recipe_details: {str(e)}", exc_info=True)
        raise

# --- Internal Helper Functions for FoodData Central API ---
def _fooddata_get_nutrition_info(ingredient_list: List[str]) -> Dict[str, Any]:
    """
    Calls the FoodData Central API to fetch nutrition info for ingredients.
    Returns a normalized nutrition dict (sum of all ingredients).
    """
    # For simplicity, sum nutrients for all ingredients
    nutrition = {}
    for ingredient in ingredient_list:
        url = f"https://api.nal.usda.gov/fdc/v1/foods/search"
        params = {
            "api_key": FOODDATA_API_KEY,
            "query": ingredient,
            "pageSize": 1,
        }
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        foods = data.get("foods", [])
        if foods:
            food_nutrition = foods[0].get("foodNutrients", [])
            for n in food_nutrition:
                name = n.get("nutrientName")
                amount = n.get("value")
                unit = n.get("unitName")
                if name and amount is not None:
                    if name not in nutrition:
                        nutrition[name] = {"amount": 0, "unit": unit}
                    nutrition[name]["amount"] += amount
    return {"nutrition": nutrition, "source": "fooddata"}

# --- Internal Helper Functions for Spoonacular Nutrition ---
def _spoonacular_get_nutrition_info(ingredient_list: List[str]) -> Dict[str, Any]:
    """
    Calls the Spoonacular API to fetch nutrition info for a list of ingredients.
    Returns a normalized nutrition dict.
    """
    url = f"https://api.spoonacular.com/recipes/parseIngredients"
    params = {
        "apiKey": SPOONACULAR_API_KEY,
    }
    nutrition = {}
    for ingredient in ingredient_list:
        ingredient_params = params.copy()
        ingredient_params["ingredientList"] = ingredient
        ingredient_params["servings"] = 1
        response = requests.post(url, data=ingredient_params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if isinstance(data, list) and data:
            n = data[0].get("nutrition", {})
            for k, v in n.items():
                if k not in nutrition:
                    nutrition[k] = v
                else:
                    # If numeric, sum values
                    try:
                        nutrition[k] += v
                    except Exception:
                        pass
    return {"nutrition": nutrition, "source": "spoonacular"}

# --- Normalization Functions ---
def _normalize_spoonacular_recipe(recipe: Dict[str, Any]) -> Dict[str, Any]:
    """
    Converts a Spoonacular API recipe dict to the app's standard format.
    """
    # Extract ingredients
    ingredients = []
    if recipe.get("extendedIngredients"):
        ingredients = [i.get("originalString", i.get("original", "")) if isinstance(i, dict) else str(i)
                      for i in recipe.get("extendedIngredients", [])]

    # Extract instructions - Spoonacular uses analyzedInstructions array
    instructions = []
    if recipe.get("analyzedInstructions"):
        for instruction_group in recipe.get("analyzedInstructions", []):
            if isinstance(instruction_group, dict) and instruction_group.get("steps"):
                for step in instruction_group.get("steps", []):
                    if isinstance(step, dict) and step.get("step"):
                        instructions.append(step.get("step"))
    elif recipe.get("instructions"):
        # Fallback to simple instructions field if available
        instructions_text = recipe.get("instructions")
        if isinstance(instructions_text, str):
            # Split by sentences or periods for better formatting
            instructions = [inst.strip() for inst in instructions_text.split('.') if inst.strip()]

    # Extract additional metadata
    ready_in_minutes = recipe.get("readyInMinutes")
    servings = recipe.get("servings")

    return {
        "id": recipe.get("id"),
        "title": recipe.get("title"),
        "image_url": recipe.get("image"),
        "ingredients": ingredients,
        "instructions": instructions,
        "nutrition": recipe.get("nutrition", {}),
        "source": "spoonacular",
        "readyInMinutes": ready_in_minutes,
        "servings": servings,
        "summary": recipe.get("summary", ""),
    }

def _tasty_get_recipe_details(recipe_id: str) -> Dict[str, Any]:
    """
    Calls the Tasty API to get full details for a single recipe by ID.
    Returns a normalized recipe dict.
    """
    url = f"https://{TASTY_API_HOST}/recipes/get-more-info"
    headers = {
        "x-rapidapi-host": TASTY_API_HOST,
        "x-rapidapi-key": TASTY_API_KEY,
    }
    params = {"id": recipe_id}
    response = requests.get(url, headers=headers, params=params, timeout=10)
    response.raise_for_status()
    recipe = response.json()
    return _normalize_tasty_recipe(recipe)

def _normalize_tasty_recipe(recipe: Dict[str, Any]) -> Dict[str, Any]:
    """
    Converts a Tasty API recipe dict to the app's standard format.
    """
    # Extract ingredients from sections
    ingredients = []
    if recipe.get("sections"):
        for section in recipe.get("sections", []):
            if section.get("components"):
                for component in section.get("components", []):
                    if component.get("raw_text"):
                        ingredients.append(component.get("raw_text"))

    # Extract instructions
    instructions = []
    if recipe.get("instructions"):
        for instruction in recipe.get("instructions", []):
            if instruction.get("display_text"):
                instructions.append(instruction.get("display_text"))

    return {
        "id": recipe.get("id"),
        "title": recipe.get("name"),
        "image_url": (recipe.get("thumbnail_url") or recipe.get("beauty_url")),
        "ingredients": ingredients,
        "instructions": instructions,
        "nutrition": recipe.get("nutrition", {}),
        "source": "tasty",
        "readyInMinutes": recipe.get("total_time_minutes"),
        "servings": recipe.get("num_servings"),
        "summary": recipe.get("description", ""),
    }

def _normalize_themealdb_recipe(recipe: Dict[str, Any]) -> Dict[str, Any]:
    """
    Converts a TheMealDB API recipe dict to the app's standard format.
    """
    # Extract ingredients and measurements
    ingredients = []
    for i in range(1, 21):  # TheMealDB has up to 20 ingredients
        ingredient = recipe.get(f"strIngredient{i}", "")
        measure = recipe.get(f"strMeasure{i}", "")

        if ingredient and ingredient.strip():
            if measure and measure.strip():
                ingredients.append(f"{measure.strip()} {ingredient.strip()}")
            else:
                ingredients.append(ingredient.strip())

    # Extract instructions (split by periods or newlines for better formatting)
    instructions_text = recipe.get("strInstructions", "")
    instructions = []
    if instructions_text:
        # Split by sentences or newlines, filter out empty ones
        instruction_parts = instructions_text.replace('\r\n', '\n').split('\n')
        instructions = [part.strip() for part in instruction_parts if part.strip()]

        # If no newlines, try splitting by periods for long instructions
        if len(instructions) == 1 and len(instructions[0]) > 200:
            sentence_parts = instructions[0].split('. ')
            instructions = [part.strip() + '.' for part in sentence_parts if part.strip()]

    return {
        "id": recipe.get("idMeal"),
        "title": recipe.get("strMeal"),
        "image_url": recipe.get("strMealThumb"),
        "ingredients": ingredients,
        "instructions": instructions,
        "nutrition": {},  # TheMealDB doesn't provide nutrition data
        "source": "themealdb",
        "category": recipe.get("strCategory"),
        "area": recipe.get("strArea"),
        "tags": recipe.get("strTags", "").split(",") if recipe.get("strTags") else [],
        "youtube_url": recipe.get("strYoutube"),
    }

def get_nutrition_info(ingredient_list: List[str]) -> Dict[str, Any]:
    """
    Fetch nutrition info using FoodData Central.
    Falls back to Spoonacular if FoodData Central fails.
    Returns standardized nutrition data.
    """
    # Filter out None values and empty strings
    valid_ingredients = [ing for ing in ingredient_list if ing and isinstance(ing, str) and ing.strip()]
    if not valid_ingredients:
        return {"nutrition": {}, "source": "none"}

    # Create a safe cache key by hashing the ingredients
    import hashlib
    ingredients_str = ','.join(sorted(valid_ingredients))
    cache_key = f"nutrition:{hashlib.md5(ingredients_str.encode()).hexdigest()}"
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached
    try:
        nutrition = _fooddata_get_nutrition_info(valid_ingredients)
        if nutrition:
            safe_cache_set(cache_key, nutrition, timeout=RECIPE_CACHE_DURATION)
            return nutrition
    except Exception as e:
        logger.error(f"FoodData Central nutrition failed: {e}")
    # Fallback to Spoonacular
    try:
        nutrition = _spoonacular_get_nutrition_info(valid_ingredients)
        if nutrition:
            safe_cache_set(cache_key, nutrition, timeout=RECIPE_CACHE_DURATION)
            return nutrition
    except Exception as e:
        logger.error(f"Spoonacular nutrition failed: {e}")
    return {}

# --- New Search Functions for Different Search Types ---

def search_recipes_by_name(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by name/title using Spoonacular API with TheMealDB fallback.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_by_name", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    # Try Spoonacular first
    try:
        results = _spoonacular_search_by_name(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        # Check if it's a payment error (402) and log appropriately
        if hasattr(e, 'response') and e.response.status_code == 402:
            logger.warning("Spoonacular API quota exceeded (402) in name search. Falling back to TheMealDB.")
        else:
            logger.error(f"Spoonacular name search failed: {e}")

    # Fallback to TheMealDB
    try:
        results = _themealdb_search_by_name(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB name search failed: {e}")

    return []

def search_recipes_by_cuisine(cuisine: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by cuisine using TheMealDB first, then Spoonacular fallback.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_by_cuisine", cuisine, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    # Try TheMealDB first (excellent for cuisine searches)
    try:
        results = _themealdb_search_by_cuisine(cuisine, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB cuisine search failed: {e}")

    # Fallback to Spoonacular
    try:
        results = _spoonacular_search_by_cuisine(cuisine, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        # Check if it's a payment error (402) and log appropriately
        if hasattr(e, 'response') and e.response.status_code == 402:
            logger.warning("Spoonacular API quota exceeded (402) in cuisine search.")
        else:
            logger.error(f"Spoonacular cuisine search failed: {e}")

    return []

def search_recipes_by_diet(diet_restrictions: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by dietary restrictions using Spoonacular API.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("search_by_diet", diet_restrictions, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _spoonacular_search_by_diet(diet_restrictions, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"Spoonacular diet search failed: {e}")
    return []

def search_recipes_by_time(time_input: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by cooking time using Spoonacular API.
    Parses time input like "30 minutes", "under 1 hour", "quick", etc.
    Returns standardized recipe list.
    """
    # Parse time input to get max_time in minutes
    max_time = _parse_time_input(time_input)
    if max_time is None:
        logger.warning(f"Could not parse time input: {time_input}")
        return []

    cache_key = _generate_cache_key("search_by_time", str(max_time), filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _spoonacular_search_by_time(max_time, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"Spoonacular time search failed: {e}")
    return []

def _parse_time_input(time_input: str) -> Optional[int]:
    """
    Parse time input string and return max time in minutes.
    Examples: "30 minutes", "1 hour", "under 45 minutes", "quick", "fast"
    """
    import re

    time_input_lower = time_input.lower().strip()

    # Handle common keywords
    if any(keyword in time_input_lower for keyword in ['quick', 'fast', 'rapid']):
        return 30
    elif any(keyword in time_input_lower for keyword in ['slow', 'long']):
        return 180

    # Extract numbers and time units
    # Look for patterns like "30 minutes", "1 hour", "45 min", etc.
    patterns = [
        r'(\d+)\s*(?:hours?|hrs?|h)\s*(?:(\d+)\s*(?:minutes?|mins?|m))?',  # "1 hour 30 minutes" or "2 hours"
        r'(\d+)\s*(?:minutes?|mins?|m)',  # "30 minutes"
        r'under\s+(\d+)\s*(?:minutes?|mins?|m)',  # "under 30 minutes"
        r'under\s+(\d+)\s*(?:hours?|hrs?|h)',  # "under 1 hour"
        r'(\d+)',  # Just a number, assume minutes
    ]

    for pattern in patterns:
        match = re.search(pattern, time_input_lower)
        if match:
            if 'hour' in pattern:
                if match.group(2):  # Has both hours and minutes
                    hours = int(match.group(1))
                    minutes = int(match.group(2))
                    return hours * 60 + minutes
                else:  # Just hours
                    hours = int(match.group(1))
                    return hours * 60
            else:  # Minutes
                return int(match.group(1))

    # Default fallback
    return None

# --- Public TheMealDB Search Functions ---
def search_recipes_by_themealdb_name(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by name using TheMealDB API.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = _generate_cache_key("themealdb_name", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _themealdb_search_by_name(query, filters)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB name search failed: {e}")
    return []

def search_recipes_by_themealdb_cuisine(area: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes by cuisine/area using TheMealDB API.
    Returns standardized recipe list.
    """
    cache_key = _generate_cache_key("themealdb_cuisine", area, filters)
    cached = cache.get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _themealdb_search_by_cuisine(area, filters)
        if results:
            cache.set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB cuisine search failed: {e}")
    return []

def get_random_recipes(count: int = 5) -> List[Dict[str, Any]]:
    """
    Get random recipes using TheMealDB API.
    Returns standardized recipe list with robust Redis caching.
    """
    cache_key = f"themealdb_random:{count}"
    cached = safe_cache_get(cache_key)
    if cached is not None:
        return cached

    try:
        results = _themealdb_get_random_meals(count)
        if results:
            safe_cache_set(cache_key, results, timeout=RECIPE_CACHE_DURATION)
            return results
    except Exception as e:
        logger.error(f"TheMealDB random search failed: {e}")
    return []

def unified_search(search_type: str, query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Intelligent unified search function that strategically routes to appropriate APIs based on their strengths.

    API Strategy:
    - TheMealDB: Primary for cuisine searches, good for international recipes (free, reliable)
    - Spoonacular: Primary for dietary restrictions, nutrition data, detailed recipe info
    - Tasty: Limited to 10 requests/day, use for trending/popular recipes when available

    Args:
        search_type: One of 'name', 'ingredients', 'cuisine', 'dietary', 'time'
        query: The search query string
        filters: Optional additional filters

    Returns:
        List of normalized recipe dictionaries
    """
    try:
        results = []

        if search_type == 'name':
            # Strategy: Try TheMealDB first (free), then Spoonacular, then Tasty if available
            results = _intelligent_name_search(query, filters)

        elif search_type == 'ingredients':
            # Strategy: Try TheMealDB for single ingredient, then existing multi-API approach
            results = _intelligent_ingredient_search(query, filters)

        elif search_type == 'cuisine':
            # Strategy: TheMealDB is excellent for cuisine searches, use as primary
            results = _intelligent_cuisine_search(query, filters)

        elif search_type == 'dietary':
            # Strategy: Spoonacular is best for dietary restrictions
            results = search_recipes_by_diet(query, filters)

        elif search_type == 'time':
            # Strategy: Spoonacular only (TheMealDB doesn't have time data)
            results = search_recipes_by_time(query, filters)

        else:
            logger.warning(f"Unknown search type: {search_type}")
            # Default to intelligent name search
            results = _intelligent_name_search(query, filters)

        logger.debug(f"Unified search for {search_type}:'{query}' returned {len(results)} results")
        return results

    except Exception as e:
        logger.error(f"Unified search failed for type {search_type}, query {query}: {e}")
        return []

def _intelligent_name_search(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Intelligent name search using multiple APIs strategically.
    Now includes caching to prevent repeated API calls.
    """
    # Add caching to prevent repeated API calls
    cache_key = _generate_cache_key("intelligent_name_search", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        logger.debug(f"Returning cached intelligent name search results for '{query}'")
        return cached

    results = []

    try:
        # 1. Try TheMealDB first (free, good coverage)
        themealdb_results = _themealdb_search_by_name(query, filters)
        results.extend(themealdb_results)
        logger.debug(f"TheMealDB name search returned {len(themealdb_results)} results")

        # 2. Try Spoonacular for more diverse results (handle payment errors gracefully)
        try:
            spoonacular_results = _spoonacular_search_by_name(query, filters)
            # Avoid duplicates by checking titles
            existing_titles = {r.get('title', '').lower() for r in results}
            new_spoonacular = [r for r in spoonacular_results
                             if r.get('title', '').lower() not in existing_titles]
            results.extend(new_spoonacular)
            logger.debug(f"Spoonacular added {len(new_spoonacular)} new results")
        except Exception as e:
            # Check if it's a payment error (402) and log appropriately
            if hasattr(e, 'response') and e.response.status_code == 402:
                logger.warning("Spoonacular API quota exceeded (402) in intelligent name search. Continuing with other sources.")
            else:
                logger.error(f"Spoonacular API failed in intelligent name search: {e}")

        # 3. If still need more and Tasty is available, try it
        if len(results) < 25 and _can_use_tasty_api():
            try:
                tasty_results = _tasty_search_recipes(query, filters)
                existing_titles = {r.get('title', '').lower() for r in results}
                new_tasty = [r for r in tasty_results
                            if r.get('title', '').lower() not in existing_titles]
                results.extend(new_tasty)
                logger.debug(f"Tasty added {len(new_tasty)} new results")
            except Exception as e:
                logger.error(f"Tasty API failed in intelligent name search: {e}")

    except Exception as e:
        logger.error(f"Error in intelligent name search: {e}")

    final_results = results[:30]  # Increased limit to 30 results

    # Cache the results for 1 day
    safe_cache_set(cache_key, final_results, timeout=RECIPE_CACHE_DURATION)
    logger.debug(f"Cached intelligent name search results for '{query}' with {len(final_results)} recipes")

    return final_results

def get_additional_spoonacular_results(query: str, search_type: str, existing_recipes: List[Dict[str, Any]], limit: int = 20) -> List[Dict[str, Any]]:
    """
    Get additional diverse results from Spoonacular API for masonry grid.
    Uses different search strategies to get more varied results.
    Results are cached to ensure consistency.
    """
    # Generate cache key for additional results
    existing_ids = sorted([f"{r.get('source', '')}:{r.get('id', '')}" for r in existing_recipes if r.get('id')])
    existing_hash = hashlib.md5(json.dumps(existing_ids, sort_keys=True).encode()).hexdigest()[:16]

    cache_key = f"additional_results:{search_type}:{query}:{existing_hash}:{limit}"

    # Check cache first
    cached_results = safe_cache_get(cache_key)
    if cached_results is not None:
        logger.debug(f"Using cached additional results for key: {cache_key}")
        return cached_results

    additional_results = []
    existing_titles = {r.get('title', '').lower() for r in existing_recipes}

    try:
        # Strategy 1: Use broader search terms
        if search_type == 'name':
            # Extract key words from the query for broader search
            keywords = query.split()
            if len(keywords) > 1:
                # Try searching with individual keywords
                for keyword in keywords[:2]:  # Limit to first 2 keywords
                    if len(keyword) > 3:  # Only meaningful words
                        broader_results = _spoonacular_search_by_name(keyword, {'number': 10})
                        new_results = [r for r in broader_results
                                     if r.get('title', '').lower() not in existing_titles]
                        additional_results.extend(new_results)
                        existing_titles.update(r.get('title', '').lower() for r in new_results)

                        if len(additional_results) >= limit:
                            break

        # Strategy 2: Get popular/trending recipes if we still need more
        if len(additional_results) < limit:
            try:
                popular_results = _spoonacular_search_by_name('popular', {'number': 15, 'sort': 'popularity'})
                new_popular = [r for r in popular_results
                             if r.get('title', '').lower() not in existing_titles]
                additional_results.extend(new_popular)
                existing_titles.update(r.get('title', '').lower() for r in new_popular)
            except Exception as e:
                logger.debug(f"Popular recipes search failed: {e}")

        # Strategy 3: Diverse recipes with deterministic sorting if still need more
        if len(additional_results) < limit:
            try:
                # Use popularity sorting instead of random for consistent results
                diverse_results = _spoonacular_search_by_name('healthy', {'number': 20, 'sort': 'popularity'})
                new_diverse = [r for r in diverse_results
                             if r.get('title', '').lower() not in existing_titles]
                additional_results.extend(new_diverse)
            except Exception as e:
                logger.debug(f"Diverse recipes search failed: {e}")

        # Limit results and cache them
        final_results = additional_results[:limit]

        # Cache the results for 1 day (same as recipe data)
        safe_cache_set(cache_key, final_results, timeout=RECIPE_CACHE_DURATION)
        logger.debug(f"Cached additional results with key: {cache_key}")

        logger.debug(f"Got {len(final_results)} additional Spoonacular results")
        return final_results

    except Exception as e:
        logger.error(f"Error getting additional Spoonacular results: {e}")
        return []














def _restricted_ingredient_search(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Search for recipes that use ONLY the specified ingredients.
    This is a more restrictive search that filters out recipes with additional ingredients.

    Strategy:
    - Parse the user's ingredient list
    - Search for recipes using normal ingredient search
    - Filter results to only include recipes that don't have extra ingredients
    """
    try:
        # Parse ingredients from query
        user_ingredients = [ing.strip().lower() for ing in query.split(',') if ing.strip()]
        if not user_ingredients:
            return []

        logger.debug(f"Restricted search for ingredients: {user_ingredients}")

        # Get all recipes that contain these ingredients (using normal search)
        temp_filters = dict(filters) if filters else {}
        temp_filters.pop('restrict_ingredients', None)  # Remove restriction flag for the base search

        all_recipes = []

        # Try TheMealDB first for single ingredients
        if len(user_ingredients) == 1:
            try:
                themealdb_results = _themealdb_search_by_ingredient(user_ingredients[0], temp_filters)
                all_recipes.extend(themealdb_results)
            except Exception as e:
                logger.error(f"TheMealDB restricted search failed: {e}")

        # Try Spoonacular for multi-ingredient or additional results
        try:
            spoonacular_results = _spoonacular_search_recipes(query, temp_filters)
            existing_titles = {r.get('title', '').lower() for r in all_recipes}
            new_spoonacular = [r for r in spoonacular_results
                             if r.get('title', '').lower() not in existing_titles]
            all_recipes.extend(new_spoonacular)
        except Exception as e:
            logger.error(f"Spoonacular restricted search failed: {e}")

        # Filter recipes to only include those with matching ingredients
        restricted_recipes = []
        for recipe in all_recipes:
            recipe_ingredients = recipe.get('ingredients', [])
            if not recipe_ingredients:
                # If no ingredients data, include the recipe (benefit of doubt)
                restricted_recipes.append(recipe)
                continue

            # Convert recipe ingredients to lowercase for comparison
            recipe_ingredient_names = []
            for ingredient in recipe_ingredients:
                if isinstance(ingredient, str):
                    recipe_ingredient_names.append(ingredient.lower())
                elif isinstance(ingredient, dict):
                    name = ingredient.get('name', '') or ingredient.get('original', '')
                    if name:
                        recipe_ingredient_names.append(name.lower())

            # Check if recipe uses only the specified ingredients
            if _recipe_uses_only_specified_ingredients(recipe_ingredient_names, user_ingredients):
                restricted_recipes.append(recipe)
            elif len(recipe_ingredient_names) <= len(user_ingredients) + 3:
                # If recipe has few ingredients (user ingredients + max 3 common ones), include it
                restricted_recipes.append(recipe)

        logger.debug(f"Restricted ingredient search found {len(restricted_recipes)} recipes from {len(all_recipes)} total")
        return restricted_recipes[:20]  # Limit results

    except Exception as e:
        logger.error(f"Error in restricted ingredient search: {e}")
        return []


def _recipe_uses_only_specified_ingredients(recipe_ingredients: List[str], user_ingredients: List[str]) -> bool:
    """
    Check if a recipe uses only the ingredients specified by the user.
    This is a simplified check that looks for key ingredient matches.
    """
    try:
        # Common ingredients that are usually acceptable in "restricted" searches
        common_acceptable = {
            'salt', 'pepper', 'water', 'oil', 'olive oil', 'vegetable oil',
            'garlic', 'onion', 'black pepper', 'white pepper', 'sea salt',
            'cooking oil', 'butter', 'margarine', 'lemon', 'lime', 'herbs',
            'spices', 'seasoning', 'bay leaves', 'thyme', 'parsley', 'cilantro'
        }

        for recipe_ingredient in recipe_ingredients:
            recipe_ingredient = recipe_ingredient.lower().strip()

            # Skip empty ingredients
            if not recipe_ingredient:
                continue

            # Skip common acceptable ingredients
            if any(common in recipe_ingredient for common in common_acceptable):
                continue

            # Check if this recipe ingredient matches any user ingredient
            ingredient_found = False
            for user_ingredient in user_ingredients:
                if user_ingredient in recipe_ingredient or recipe_ingredient in user_ingredient:
                    ingredient_found = True
                    break

            # If we found an ingredient that's not in the user's list and not common, reject this recipe
            if not ingredient_found:
                return False

        return True

    except Exception as e:
        logger.error(f"Error checking ingredient restriction: {e}")
        return False


def _intelligent_ingredient_search(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Intelligent ingredient search using multiple APIs strategically.
    Now includes caching to prevent repeated API calls.

    Strategy:
    - Single ingredient: TheMealDB first (free, reliable)
    - Multi-ingredient: Spoonacular first (better for complex queries), Tasty as fallback
    - If restrict_ingredients is True: Only search for recipes that use ONLY the specified ingredients
    """
    # Add caching to prevent repeated API calls
    cache_key = _generate_cache_key("intelligent_ingredient_search", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        logger.debug(f"Returning cached intelligent ingredient search results for '{query}'")
        return cached

    # Check if ingredients should be restricted to only those specified
    restrict_ingredients = filters and filters.get('restrict_ingredients', False)

    if restrict_ingredients:
        logger.debug(f"Performing restricted ingredient search for: '{query}'")
        return _restricted_ingredient_search(query, filters)

    results = []

    try:
        # Parse ingredients to determine search strategy
        ingredients = [ing.strip() for ing in query.split(',')]
        is_multi_ingredient = len(ingredients) > 1 or ',' in query

        if not is_multi_ingredient and len(ingredients) == 1 and ingredients[0]:
            # Single ingredient - try TheMealDB first (free and good for single ingredients)
            try:
                themealdb_results = _themealdb_search_by_ingredient(ingredients[0], filters)
                results.extend(themealdb_results)
                logger.debug(f"TheMealDB ingredient search returned {len(themealdb_results)} results")
            except Exception as e:
                logger.error(f"TheMealDB ingredient search failed: {e}")

        # For multi-ingredient searches or if we need more results
        if is_multi_ingredient or len(results) < 10:
            # Strategy: Spoonacular first for multi-ingredient (better complex query handling)
            try:
                spoonacular_results = _spoonacular_search_recipes(query, filters)
                existing_titles = {r.get('title', '').lower() for r in results}
                new_spoonacular = [r for r in spoonacular_results
                                 if r.get('title', '').lower() not in existing_titles]
                results.extend(new_spoonacular)
                logger.debug(f"Spoonacular ingredient search added {len(new_spoonacular)} new results")
            except Exception as e:
                logger.error(f"Spoonacular ingredient search failed: {e}")

        # Fallback to Tasty API if we still need more results and it's available
        if len(results) < 15 and _can_use_tasty_api():
            try:
                tasty_results = _tasty_search_recipes(query, filters)
                existing_titles = {r.get('title', '').lower() for r in results}
                new_tasty = [r for r in tasty_results
                           if r.get('title', '').lower() not in existing_titles]
                results.extend(new_tasty)
                logger.debug(f"Tasty ingredient search added {len(new_tasty)} new results")
            except Exception as e:
                logger.error(f"Tasty ingredient search failed: {e}")

    except Exception as e:
        logger.error(f"Error in intelligent ingredient search: {e}")

    final_results = results[:20]

    # Cache the results for 1 day
    safe_cache_set(cache_key, final_results, timeout=RECIPE_CACHE_DURATION)
    logger.debug(f"Cached intelligent ingredient search results for '{query}' with {len(final_results)} recipes")

    return final_results

def _intelligent_cuisine_search(query: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Intelligent cuisine search using multiple APIs strategically.
    Now includes caching to prevent repeated API calls.
    """
    # Add caching to prevent repeated API calls
    cache_key = _generate_cache_key("intelligent_cuisine_search", query, filters)
    cached = safe_cache_get(cache_key)
    if cached is not None:
        logger.debug(f"Returning cached intelligent cuisine search results for '{query}'")
        return cached

    results = []

    try:
        # TheMealDB is excellent for cuisine searches - use as primary
        themealdb_results = _themealdb_search_by_cuisine(query, filters)
        results.extend(themealdb_results)
        logger.debug(f"TheMealDB cuisine search returned {len(themealdb_results)} results")

        # If we need more results, try Spoonacular
        if len(results) < 15:
            spoonacular_results = _spoonacular_search_by_cuisine(query, filters)
            existing_titles = {r.get('title', '').lower() for r in results}
            new_spoonacular = [r for r in spoonacular_results
                             if r.get('title', '').lower() not in existing_titles]
            results.extend(new_spoonacular)
            logger.debug(f"Spoonacular cuisine search added {len(new_spoonacular)} new results")

    except Exception as e:
        logger.error(f"Error in intelligent cuisine search: {e}")

    final_results = results[:20]

    # Cache the results for 1 day
    safe_cache_set(cache_key, final_results, timeout=RECIPE_CACHE_DURATION)
    logger.debug(f"Cached intelligent cuisine search results for '{query}' with {len(final_results)} recipes")

    return final_results

# --- Internal helpers for API requests, normalization, and caching will be added here ---

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
