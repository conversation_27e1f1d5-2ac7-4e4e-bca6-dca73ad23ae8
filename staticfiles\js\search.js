// search.js - extracted from search.html

// Food-themed loading animation controller
const FoodLoader = {
  overlayElement: null,
  inlineElement: null,
  skeletonsElement: null,
  resultsContainer: null,

  init() {
    this.overlayElement = document.getElementById('food-loader-overlay');
    this.inlineElement = document.getElementById('recipe-results-loading');
    this.skeletonsElement = document.getElementById('recipe-skeletons');
    this.resultsContainer = document.getElementById('recipe-results');

    console.log('🎯 SimpleLoader initialized:', {
      overlay: !!this.overlayElement,
      inline: !!this.inlineElement,
      skeletons: !!this.skeletonsElement,
      results: !!this.resultsContainer
    });
  },

  showOverlay() {
    if (this.overlayElement) {
      this.overlayElement.classList.add('show');
    }
  },

  hideOverlay() {
    if (this.overlayElement) {
      this.overlayElement.classList.remove('show');
    }
  },

  showInlineLoader() {
    if (this.inlineElement) {
      this.inlineElement.style.display = 'flex';
    }
    if (this.resultsContainer) {
      this.resultsContainer.style.display = 'none';
    }
  },

  hideInlineLoader() {
    if (this.inlineElement) {
      this.inlineElement.style.display = 'none';
    }
    if (this.resultsContainer) {
      this.resultsContainer.style.display = 'block';
    }
  },

  showSkeletons() {
    if (this.skeletonsElement) {
      this.skeletonsElement.style.display = 'block';
    }
    if (this.resultsContainer) {
      this.resultsContainer.style.display = 'none';
    }
  },

  hideSkeletons() {
    if (this.skeletonsElement) {
      this.skeletonsElement.style.display = 'none';
    }
    if (this.resultsContainer) {
      this.resultsContainer.style.display = 'block';
    }
  },

  showSearchButtonSpinner() {
    const searchIcons = [
      document.getElementById('search-loading-icon'),
      document.getElementById('mobile-search-loading-icon')
    ];

    searchIcons.forEach(icon => {
      if (icon) {
        icon.classList.add('active');
      }
    });

    // Hide search button text and icons
    const searchButtons = document.querySelectorAll('.search-btn-primary .desktop-text, .search-btn-primary svg:not(.search-loading-spinner), .search-btn svg:not(.search-loading-spinner)');
    searchButtons.forEach(btn => {
      if (btn && !btn.closest('.search-loading-icon')) {
        btn.style.display = 'none';
      }
    });
  },

  hideSearchButtonSpinner() {
    const searchIcons = [
      document.getElementById('search-loading-icon'),
      document.getElementById('mobile-search-loading-icon')
    ];

    searchIcons.forEach(icon => {
      if (icon) {
        icon.classList.remove('active');
      }
    });

    // Show search button text and icons
    const searchButtons = document.querySelectorAll('.search-btn-primary .desktop-text, .search-btn-primary svg:not(.search-loading-spinner), .search-btn svg:not(.search-loading-spinner)');
    searchButtons.forEach(btn => {
      if (btn && !btn.closest('.search-loading-icon')) {
        btn.style.display = '';
      }
    });
  }
};

// Helper to add/remove margin and manage mobile navigation visibility
function setSearchTriggered(triggered) {
  const hero = document.getElementById('hero-section');
  const searchRow = document.getElementById('search-ui-row');
  const resultsRow = document.getElementById('results-row');
  const topNavBar = document.getElementById('top-nav-bar');

  // Mobile navigation elements
  const mobileHamburger = document.querySelector('.mobile-hamburger');
  const mobileBottomNav = document.querySelector('.mobile-bottom-nav');
  const footer = document.querySelector('footer');
  const body = document.body;

  if (triggered) {
    // Hide hero and original search elements
    if (hero) hero.style.display = 'none';
    if (searchRow) {
      searchRow.style.display = 'none';
    }

    // Show results
    if (resultsRow) resultsRow.style.display = '';

    // Show top navigation bar with "My Recipy Finder" header
    if (topNavBar) {
      topNavBar.style.display = 'flex';
      body.classList.add('top-nav-active');
    }

    // Hide hamburger menu and footer on mobile screens during search, but keep bottom nav visible
    if (window.innerWidth <= 768) {
      if (mobileHamburger) mobileHamburger.style.display = 'none';
      // Keep mobile bottom nav visible during search
      if (mobileBottomNav) mobileBottomNav.style.display = 'flex';
      if (footer) footer.style.display = 'none';

      // Add class to body to indicate search state for additional styling
      body.classList.add('mobile-search-active');
    }

  } else {
    // Show hero and original search elements
    if (hero) hero.style.display = '';
    if (searchRow) {
      searchRow.style.display = '';
      searchRow.classList.remove('mt-5');
      searchRow.classList.add('mt-0');
    }

    // Hide results
    if (resultsRow) {
      resultsRow.style.display = 'none';
    }

    // Hide top navigation bar
    if (topNavBar) {
      topNavBar.style.display = 'none';
      body.classList.remove('top-nav-active');
    }

    // Show mobile navigation and footer when not searching
    if (mobileHamburger) mobileHamburger.style.display = '';
    if (mobileBottomNav) {
      // Restore mobile bottom nav display based on screen size
      if (window.innerWidth <= 768) {
        mobileBottomNav.style.display = 'flex';
      } else {
        mobileBottomNav.style.display = 'none';
      }
    }
    if (footer) {
      // Show footer only on desktop, hide on mobile when bottom nav is present
      if (window.innerWidth <= 768) {
        footer.style.display = 'none';
      } else {
        footer.style.display = '';
      }
    }

    // Remove search state class from body
    body.classList.remove('mobile-search-active');

    // Reset scroll expansion when search is cleared
    if (typeof RecipeScrollExpansion !== 'undefined') {
      RecipeScrollExpansion.reset();
    }
  }
}

// Handle window resize to manage mobile navigation visibility
window.addEventListener('resize', function() {
  const body = document.body;
  const mobileHamburger = document.querySelector('.mobile-hamburger');
  const mobileBottomNav = document.querySelector('.mobile-bottom-nav');
  const footer = document.querySelector('footer');

  // If we're in search mode
  if (body.classList.contains('mobile-search-active')) {
    if (window.innerWidth <= 768) {
      // Hide hamburger and footer on mobile screens during search, but keep bottom nav visible
      if (mobileHamburger) mobileHamburger.style.display = 'none';
      if (mobileBottomNav) mobileBottomNav.style.display = 'flex';
      if (footer) footer.style.display = 'none';
    } else {
      // Show elements on desktop screens during search
      if (mobileHamburger) mobileHamburger.style.display = '';
      if (mobileBottomNav) mobileBottomNav.style.display = 'none'; // Always hidden on desktop
      if (footer) footer.style.display = '';
    }
  } else {
    // Not in search mode - restore normal visibility based on screen size
    if (window.innerWidth <= 768) {
      // Mobile: show bottom nav, hide hamburger and footer
      if (mobileHamburger) mobileHamburger.style.display = '';
      if (mobileBottomNav) mobileBottomNav.style.display = 'flex';
      if (footer) footer.style.display = 'none';
    } else {
      // Desktop: show hamburger and footer, hide bottom nav
      if (mobileHamburger) mobileHamburger.style.display = 'none';
      if (mobileBottomNav) mobileBottomNav.style.display = 'none';
      if (footer) footer.style.display = '';
    }
  }
});

// Recipe Results Scroll Expansion Controller
const RecipeScrollExpansion = {
  isExpanded: false,
  scrollThreshold: 100, // Pixels to scroll before expansion
  lastScrollTop: 0,

  init() {
    this.bindScrollEvents();
    this.bindIndicatorEvents();
    console.log('🔄 RecipeScrollExpansion initialized');
  },

  bindScrollEvents() {
    let ticking = false;

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    });
  },

  bindIndicatorEvents() {
    // Add click handler for scroll expansion indicator
    document.addEventListener('click', (e) => {
      if (e.target.closest('.scroll-expansion-indicator')) {
        e.preventDefault();
        this.toggleExpansion();
      }
    });

    // Add escape key handler to exit expansion
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isExpanded) {
        this.contractResults();
      }
    });

    // Add resize handler to adjust expansion layout when window is resized
    window.addEventListener('resize', () => {
      if (this.isExpanded) {
        // Re-apply expansion with updated dimensions
        this.expandResults();
      }
    });
  },

  handleScroll() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const resultsContainer = document.getElementById('recipe-results');
    const resultsRow = document.getElementById('results-row');

    // Only apply scroll expansion when search results are visible
    if (!resultsContainer || !resultsRow || resultsRow.style.display === 'none') {
      return;
    }

    // Check if user has scrolled down past threshold
    if (scrollTop > this.scrollThreshold && !this.isExpanded) {
      this.expandResults();
    } else if (scrollTop <= this.scrollThreshold && this.isExpanded) {
      this.contractResults();
    }

    this.lastScrollTop = scrollTop;
    this.updateIndicatorVisibility();
  },

  expandResults() {
    const resultsContainer = document.getElementById('recipe-results');
    const body = document.body;

    if (!resultsContainer || this.isExpanded) return;

    console.log('📈 Expanding recipe results to full screen');

    // Add expansion class to body for global styling
    body.classList.add('recipe-results-expanded');

    // Calculate positioning to account for sidebar and mobile navigation
    const sidebar = document.querySelector('.sidebar');
    const mobileBottomNav = document.querySelector('.mobile-bottom-nav');

    let leftOffset = '0';
    let widthAdjustment = '100vw';
    let heightAdjustment = '100vh';

    // Adjust for sidebar on desktop/tablet only (not on mobile)
    if (sidebar && window.innerWidth > 768) {
      const sidebarWidth = sidebar.offsetWidth || 80; // Default sidebar width
      leftOffset = `${sidebarWidth}px`;
      widthAdjustment = `calc(100vw - ${sidebarWidth}px)`;
    }

    // Adjust for mobile bottom navigation (only on mobile screens)
    if (mobileBottomNav && window.innerWidth <= 768) {
      const navHeight = mobileBottomNav.offsetHeight || 60; // Default nav height
      heightAdjustment = `calc(100vh - ${navHeight}px)`;
    }

    // Apply expansion styles to results container
    resultsContainer.style.transition = 'all 0.3s ease-in-out';
    resultsContainer.style.position = 'fixed';
    resultsContainer.style.top = '0';
    resultsContainer.style.left = leftOffset;
    resultsContainer.style.width = widthAdjustment;
    resultsContainer.style.height = heightAdjustment;
    resultsContainer.style.zIndex = '1000';
    resultsContainer.style.backgroundColor = '#fff';
    resultsContainer.style.overflowY = 'auto';
    resultsContainer.style.padding = '1rem';

    // Hide other content during expansion
    const searchContainer = document.querySelector('.search-container');
    const heroSection = document.getElementById('hero-section');

    if (searchContainer) searchContainer.style.display = 'none';
    if (heroSection) heroSection.style.display = 'none';

    this.isExpanded = true;
    this.updateIndicatorVisibility();
  },

  contractResults() {
    const resultsContainer = document.getElementById('recipe-results');
    const body = document.body;

    if (!resultsContainer || !this.isExpanded) return;

    console.log('📉 Contracting recipe results to normal view');

    // Remove expansion class from body
    body.classList.remove('recipe-results-expanded');

    // Reset styles to normal
    resultsContainer.style.transition = 'all 0.3s ease-in-out';
    resultsContainer.style.position = '';
    resultsContainer.style.top = '';
    resultsContainer.style.left = '';
    resultsContainer.style.width = '';
    resultsContainer.style.height = '';
    resultsContainer.style.zIndex = '';
    resultsContainer.style.backgroundColor = '';
    resultsContainer.style.overflowY = '';
    resultsContainer.style.padding = '';

    // Show other content again
    const searchContainer = document.querySelector('.search-container');
    const heroSection = document.getElementById('hero-section');

    if (searchContainer) searchContainer.style.display = '';
    if (heroSection) heroSection.style.display = '';

    this.isExpanded = false;
    this.updateIndicatorVisibility();
  },

  toggleExpansion() {
    if (this.isExpanded) {
      this.contractResults();
    } else {
      this.expandResults();
    }
  },

  updateIndicatorVisibility() {
    const indicator = document.getElementById('scroll-expansion-indicator');
    const resultsContainer = document.getElementById('recipe-results');
    const resultsRow = document.getElementById('results-row');

    if (!indicator) return;

    // Show indicator only when search results are visible and not expanded
    if (resultsContainer && resultsRow && resultsRow.style.display !== 'none' && !this.isExpanded) {
      indicator.style.display = 'block';

      // Update indicator text based on current state
      const scrollText = indicator.querySelector('.scroll-text');
      if (scrollText) {
        scrollText.textContent = 'Click for full-screen view';
      }
    } else {
      indicator.style.display = 'none';
    }
  },

  // Method to reset expansion state (useful when search is cleared)
  reset() {
    if (this.isExpanded) {
      this.contractResults();
    }
    this.updateIndicatorVisibility();
  }
};

// Recipe card click handler
function initRecipeCardClickHandlers() {
  // Use event delegation to handle clicks on recipe cards that may be dynamically loaded
  document.addEventListener('click', function(event) {
    // Check if the clicked element or its parent is a recipe card
    const recipeCard = event.target.closest('[data-recipe-url]');

    if (recipeCard) {
      const recipeUrl = recipeCard.getAttribute('data-recipe-url');
      console.log('Recipe card clicked:', recipeCard);
      console.log('Recipe URL:', recipeUrl);

      // Only navigate if we have a valid URL (not '#')
      if (recipeUrl && recipeUrl !== '#') {
        event.preventDefault();
        console.log('Navigating to recipe:', recipeUrl);

        // Add a subtle loading effect
        recipeCard.style.opacity = '0.7';
        recipeCard.style.transform = 'scale(0.98)';

        // Navigate to the recipe detail page
        window.location.href = recipeUrl;
      } else {
        console.log('Invalid recipe URL, not navigating');
      }
    }
  });
}

// Attach to search form submit
window.addEventListener('DOMContentLoaded', function () {
  // Initialize food loader
  FoodLoader.init();

  // Initialize scroll expansion
  RecipeScrollExpansion.init();

  // Initialize recipe card click handlers
  initRecipeCardClickHandlers();

  var searchForm = document.querySelector('#main-search-box form');
  if (searchForm) {
    searchForm.addEventListener('submit', function () {
      // Only proceed if the Alpine.js handler allows it
      const alpineData = searchForm._x_dataStack && searchForm._x_dataStack[0];
      if (alpineData && alpineData.searchConfirmationNeeded) {
        // First click - confirmation needed, don't proceed
        return;
      }

      setTimeout(function () { setSearchTriggered(true); }, 100); // allow htmx/ajax to process
    });
  }

  // HTMX event listeners for food-themed loading animations
  document.body.addEventListener('htmx:beforeRequest', function(event) {
    // Check if this is a recipe search request
    if (event.detail.target && event.detail.target.id === 'recipe-results') {
      console.log('🍳 Starting recipe search - showing food loader');

      // Show search button spinner
      FoodLoader.showSearchButtonSpinner();

      // Show inline loader in results area (since horizontal layout was removed)
      console.log('📱 Showing inline loader');
      FoodLoader.showInlineLoader();
    }
  });

  document.body.addEventListener('htmx:afterRequest', function(event) {
    // Check if this is a recipe search request
    if (event.detail.target && event.detail.target.id === 'recipe-results') {
      console.log('✅ Recipe search completed - hiding food loader');

      // Hide search button spinner
      FoodLoader.hideSearchButtonSpinner();

      // Hide all loaders
      FoodLoader.hideOverlay();
      FoodLoader.hideInlineLoader();
      FoodLoader.hideSkeletons();

      // Ensure search triggered state is set after HTMX completes
      setSearchTriggered(true);

      // Update Alpine.js state after HTMX request completes
      setTimeout(function() {
        const searchForm = document.querySelector('#main-search-box form');
        if (searchForm && searchForm._x_dataStack && searchForm._x_dataStack[0]) {
          searchForm._x_dataStack[0].checkForExistingResults();
        }

        // Update scroll expansion indicator visibility
        if (typeof RecipeScrollExpansion !== 'undefined') {
          RecipeScrollExpansion.updateIndicatorVisibility();
        }
      }, 100);
    }
  });

  document.body.addEventListener('htmx:responseError', function(event) {
    // Handle errors by hiding loaders
    if (event.detail.target && event.detail.target.id === 'recipe-results') {
      console.log('Recipe search error - hiding food loader');
      FoodLoader.hideSearchButtonSpinner();
      FoodLoader.hideOverlay();
      FoodLoader.hideInlineLoader();
      FoodLoader.hideSkeletons();
    }
  });

  document.body.addEventListener('htmx:timeout', function(event) {
    // Handle timeouts by hiding loaders
    if (event.detail.target && event.detail.target.id === 'recipe-results') {
      console.log('Recipe search timeout - hiding food loader');
      FoodLoader.hideSearchButtonSpinner();
      FoodLoader.hideOverlay();
      FoodLoader.hideInlineLoader();
      FoodLoader.hideSkeletons();
    }
  });


});
