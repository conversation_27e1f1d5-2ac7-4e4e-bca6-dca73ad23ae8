{% extends 'recipes/base.html' %}
{% load static %}

{% block title %}Find Recipes - Recipe Finder{% endblock %}

{% block content %}
<div class="center-hero-container">
    
  <div class="container-fluid px-2">

    <!-- Hero Section (only before first search) -->
    <div class="row justify-content-center mb-5 hero-section" id="hero-section">
      <div class="col-md-8 text-center">
        <img src="{% static 'images\logos\Recipe-Finder-logo1.webp' %}" alt="Recipe Finder Logo" class="logo"
          style="max-width:250px;" />
        <h1 class="main-hero-title mb-4">What do you want to cook?</h1>
        <p class="lead mb-4">Discover the perfect recipe for any occasion</p>
      </div>
    </div>

    <!-- Search UI -->
    <div class="row justify-content-center" id="search-ui-row">
      <div class="col-md-8 text-center">

        <!-- Advanced Filters -->
        <div id="advanced-filters">
          {% include 'recipes/components/_advanced_filters.html' %}
        </div>

        <!-- Main Search Box -->
        <div class="search-container" id="main-search-box">
          {% include 'recipes/components/_main_search_box.html' %}
        </div>

        <!-- Quick Filters -->
        <div id="quick-filters">
          {% include 'recipes/components/_quick_filters.html' %}
        </div>
      </div>
    </div>

    <!-- Results -->
    <div id="results" class="mt-5 px-3">
      <div class="row" id="results-row" style="display:none;">
        <div class="col-12">
          <!-- Food-themed loading animations -->
          {% include 'recipes/components/_food_loading_animation.html' %}

          <div id="recipe-results">
            {% include 'recipes/partials/recipe_results_api.html' %}
          </div>
        </div>
      </div>
    </div>

    <!-- Legacy Loader (fallback) -->
    <div class="loader-overlay" id="loader-overlay" style="display:none;">
      <div class="loader"></div>
    </div>

  </div>
</div>

<script src="{% static 'js/search.js' %}"></script>

<script>
// Auto-trigger search if URL has search parameters (for direct access/reload)
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const searchQuery = urlParams.get('search_query');
    const searchType = urlParams.get('search_type');

    if (searchQuery && searchQuery.trim()) {
        // Set the search form values
        const searchInput = document.querySelector('input[name="search_query"]');
        const searchTypeSelect = document.querySelector('select[name="search_type"], input[name="search_type"]');
        const cuisineInput = document.querySelector('input[name="cuisine"]');

        if (searchInput) {
            searchInput.value = searchQuery;
        }

        if (searchTypeSelect && searchType) {
            if (searchTypeSelect.tagName === 'SELECT') {
                searchTypeSelect.value = searchType;
            } else {
                searchTypeSelect.value = searchType;
            }

            // Update Alpine.js data if available
            const searchForm = document.querySelector('#main-search-box form');
            if (searchForm && searchForm._x_dataStack && searchForm._x_dataStack[0]) {
                searchForm._x_dataStack[0].filter = searchType;
            }
        }

        // Set cuisine if present
        const cuisine = urlParams.get('cuisine');
        if (cuisineInput && cuisine) {
            cuisineInput.value = cuisine;
        }

        // Trigger the search automatically
        setTimeout(function() {
            const searchForm = document.querySelector('#main-search-box form');
            if (searchForm) {
                // Trigger HTMX request
                htmx.trigger(searchForm, 'submit');
                // Also trigger the UI changes
                setSearchTriggered(true);
            }
        }, 100);
    }
});
</script>
{% endblock %}


{% block extra_css %}
<!-- All CSS is now consolidated in base.css, components.css, and pages.css -->
{% endblock %}